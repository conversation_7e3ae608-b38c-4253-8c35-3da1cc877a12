#!/bin/bash

# ZChat EdgeOne 部署脚本
# 使用方法: ./deploy.sh [环境]
# 环境: dev | staging | production

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-production}

if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|production)$ ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    log_info "使用方法: ./deploy.sh [dev|staging|production]"
    exit 1
fi

log_info "开始部署到 $ENVIRONMENT 环境..."

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    npm install --legacy-peer-deps
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 设置环境变量
    export NODE_ENV=production
    export VITE_APP_ENV=$ENVIRONMENT
    
    # 执行构建
    npm run build
    
    if [ ! -d "dist" ]; then
        log_error "构建失败，dist目录不存在"
        exit 1
    fi
    
    log_success "项目构建完成"
}

# 优化构建产物
optimize_build() {
    log_info "优化构建产物..."
    
    # 创建必要的文件
    cd dist
    
    # 创建 _headers 文件用于 EdgeOne
    cat > _headers << EOF
/*
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

/manifest.json
  Content-Type: application/manifest+json

/*.js
  Cache-Control: public, max-age=31536000, immutable

/*.css
  Cache-Control: public, max-age=31536000, immutable

/*.png
  Cache-Control: public, max-age=2592000

/*.svg
  Cache-Control: public, max-age=2592000

/*.ico
  Cache-Control: public, max-age=2592000

/index.html
  Cache-Control: public, max-age=3600
EOF

    # 创建 _redirects 文件用于 SPA 路由
    cat > _redirects << EOF
# SPA 路由重定向
/*    /index.html   200
EOF

    cd ..
    log_success "构建产物优化完成"
}

# 部署到 EdgeOne
deploy_to_edgeone() {
    log_info "部署到 EdgeOne..."
    
    case $ENVIRONMENT in
        "dev")
            SITE_ID="your-dev-site-id"
            ;;
        "staging")
            SITE_ID="your-staging-site-id"
            ;;
        "production")
            SITE_ID="your-production-site-id"
            ;;
    esac
    
    # 这里需要根据实际的 EdgeOne CLI 工具进行调整
    # 示例命令（需要根据实际情况修改）
    log_info "上传文件到 EdgeOne..."
    
    # 如果使用 EdgeOne CLI
    # edgeone deploy --site-id=$SITE_ID --source=dist
    
    # 如果使用其他部署工具，请在这里添加相应命令
    log_warning "请根据实际的 EdgeOne 部署工具修改此部分"
    
    log_success "部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    case $ENVIRONMENT in
        "dev")
            URL="https://dev.zchat.example.com"
            ;;
        "staging")
            URL="https://staging.zchat.example.com"
            ;;
        "production")
            URL="https://zchat.example.com"
            ;;
    esac
    
    log_info "检查网站可访问性: $URL"
    
    # 检查网站是否可访问
    if curl -f -s "$URL" > /dev/null; then
        log_success "网站部署成功，可正常访问: $URL"
    else
        log_warning "网站可能还在部署中，请稍后检查: $URL"
    fi
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    # 这里可以添加清理逻辑
    log_success "清理完成"
}

# 主执行流程
main() {
    log_info "=== ZChat EdgeOne 部署开始 ==="
    
    check_dependencies
    install_dependencies
    build_project
    optimize_build
    deploy_to_edgeone
    verify_deployment
    cleanup
    
    log_success "=== 部署完成 ==="
    log_info "环境: $ENVIRONMENT"
    log_info "时间: $(date)"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主流程
main
