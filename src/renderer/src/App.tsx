import React, { useEffect, useState } from 'react'
import { HashRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { ConfigProvider } from 'antd'
import { store, persistor } from '@renderer/store'
import { loggerService } from '@logger'
import TopView from './components/TopView'
import AppRoutes from './routes'

const logger = loggerService.withContext('App')

// 加载组件
const LoadingComponent = () => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column'
    }}>
    <img
      src="/src/assets/images/logo.svg"
      alt="ZChat Loading"
      style={{
        width: '64px',
        height: '64px',
        marginBottom: '16px'
      }}
    />
    <div>正在加载...</div>
  </div>
)

function App(): React.ReactElement {
  const [dbReady, setDbReady] = useState(false)

  // 初始化数据库
  useEffect(() => {
    // 异步初始化数据库
    import('@renderer/databases').then(({ initDatabase }) => {
      initDatabase()
      setDbReady(true)
    }).catch((error) => {
      console.error('Database initialization failed:', error)
      setDbReady(true) // 即使失败也继续加载，避免卡住
    })
  }, [])

  // 隐藏加载动画
  useEffect(() => {
    const spinner = document.getElementById('spinner')
    if (spinner) {
      // 延迟隐藏，确保React已经渲染
      setTimeout(() => {
        spinner.style.display = 'none'
      }, 100)
    }
  }, [])

  logger.info('App initialized')

  if (!dbReady) {
    return <LoadingComponent />
  }

  return (
    <Provider store={store}>
      <PersistGate loading={<LoadingComponent />} persistor={persistor}>
        <ConfigProvider
          theme={{
            token: {
              colorPrimary: '#1890ff',
            },
          }}
        >
          <TopView>
            <HashRouter>
              <AppRoutes />
            </HashRouter>
          </TopView>
        </ConfigProvider>
      </PersistGate>
    </Provider>
  )
}

export default App
