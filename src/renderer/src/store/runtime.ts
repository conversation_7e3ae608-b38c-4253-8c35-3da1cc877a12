import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { AppLogo, UserAvatar } from '@renderer/config/env'
import type { MinAppType, Topic, WebSearchStatus } from '@renderer/types'
import type { UpdateInfo } from 'builder-util-runtime'

export interface ChatState { isMultiSelectMode: boolean
  selectedMessageIds: string[]
  activeTopic: Topic | null
  /** topic ids that are currently being renamed */  },
  renamingTopics: string[] }
  /** topic ids that are newly renamed */
}
newlyRenamedTopics: string[]
}

export interface WebSearchState {  },
  activeSearches: Record<string, WebSearchStatus>
}

export interface UpdateState { info: UpdateInfo | null
  checking: boolean
  downloading: boolean  },
  downloaded: boolean }
  downloadProgress: number
}
available: boolean
}

export interface RuntimeState { avatar: string
  generating: boolean
  /** whether the minapp popup is shown */
  minappShow: boolean
  /** the minapps that are opened and should be keep alive */
  openedKeepAliveMinapps: MinAppType[]
  /** the minapp that is opened for one time */
  openedOneOffMinapp: MinAppType | null
  /** the current minapp id */
  currentMinappId: string
  searching: boolean
  filesPath: string
  resourcesPath: string
  update: UpdateState
  },

export: ExportState }
  chat: ChatState
}
websearch: WebSearchState
}

export interface ExportState {  },
  isExporting: boolean
}

const initialState: RuntimeState = { avatar: UserAvatar,
  generating: false,
  minappShow: false,
  openedKeepAliveMinapps: [],
  openedOneOffMinapp: null,
  currentMinappId: '',
  searching: false,
  filesPath: '',
  resourcesPath: '',
  update: {
    info: null,
    checking: false,
    downloading: false,
    downloaded: false },
  downloadProgress: 0 },
  available: false
}
 },

export: { isExporting: false
 },
   },
  chat: { isMultiSelectMode: false,
    selectedMessageIds: [],
    activeTopic: null },
  renamingTopics: [] },
  newlyRenamedTopics: []
}
 },
  websearch: {  },
  activeSearches: {  },
   },
  const runtimeSlice = createSlice( { name: 'runtime' )
  initialState,)
  reducers: {)  },
  setAvatar: (state, action: PayloadAction<string | null>) => {  },
  state.avatar = action.payload || AppLogo
}
 },
    setGenerating: (state, action: PayloadAction<boolean>) => { state.generating = action.payload
 },
   },
    setMinappShow: (state, action: PayloadAction<boolean>) => { state.minappShow = action.payload
 },
   },
    setOpenedKeepAliveMinapps: (state, action: PayloadAction<MinAppType[]>) => { state.openedKeepAliveMinapps = action.payload
 },
   },
    setOpenedOneOffMinapp: (state, action: PayloadAction<MinAppType | null>) => { state.openedOneOffMinapp = action.payload
 },
   },
    setCurrentMinappId: (state, action: PayloadAction<string>) => { state.currentMinappId = action.payload
 },
   },
    setSearching: (state, action: PayloadAction<boolean>) => { state.searching = action.payload
 },
   },
    setFilesPath: (state, action: PayloadAction<string>) => { state.filesPath = action.payload
 },
   },
    setResourcesPath: (state, action: PayloadAction<string>) => { state.resourcesPath = action.payload
 },
   },
    setUpdateState: (state, action: PayloadAction<Partial<UpdateState>>) => {  },
  state.update = { ...state.update, ...action.payload  },
   },
    setExportState: (state, action: PayloadAction<Partial<ExportState>>) => {  },
  state.export = { ...state.export, ...action.payload  },
   },
    // Chat related actions
    toggleMultiSelectMode: (state, action: PayloadAction<boolean>) => { state.chat.isMultiSelectMode = action.payload
      if ( ) {
    // TODO: implement   },
   },
  3549}
  state.chat.selectedMessageIds = []
      }
    },
    setSelectedMessageIds: (state, action: PayloadAction<string[]>) => { state.chat.selectedMessageIds = action.payload
 },
   },
    setActiveTopic: (state, action: PayloadAction<Topic>) => { state.chat.activeTopic = action.payload
 },
   },
    setRenamingTopics: (state, action: PayloadAction<string[]>) => { state.chat.renamingTopics = action.payload
 },
   },
    setNewlyRenamedTopics: (state, action: PayloadAction<string[]>) => { state.chat.newlyRenamedTopics = action.payload
 },
   },
    // WebSearch related actions
    setActiveSearches: (state, action: PayloadAction<Record<string, WebSearchStatus>>) => { state.websearch.activeSearches = action.payload
 },
   },
    setWebSearchStatus: (state, action: PayloadAction<{ requestId: string; status: WebSearchStatus  },
  >) => {  },
  const { requestId, status  },
  = action.payload
      if (
) { // TODO: implement   },
   },
  4461}
 }
  delete state.websearch.activeSearches[requestId]
      }
      state.websearch.activeSearches[requestId] = status
    }

export const { setAvatar,
  setGenerating,
  setMinappShow,
  setOpenedKeepAliveMinapps,
  setOpenedOneOffMinapp,
  setCurrentMinappId,
  setSearching,
  setFilesPath,
  setResourcesPath,
  setUpdateState,
  setExportState,
  // Chat related actions
  toggleMultiSelectMode,
  setSelectedMessageIds,
  setActiveTopic,
  setRenamingTopics,
  setNewlyRenamedTopics,
  // WebSearch related actions
  setActiveSearches },
  setWebSearchStatus
} = runtimeSlice.actions

export default runtimeSlice.reducer
