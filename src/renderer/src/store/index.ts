import { configureStore } from '@reduxjs/toolkit'
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import { combineReducers } from '@reduxjs/toolkit'

// Import all reducers
import assistantsReducer from './assistants'
import settingsReducer from './settings'
import shortcutsReducer from './shortcuts'
import websearchReducer from './websearch'
import memoryReducer from './memory'
import backupReducer from './backup'
import nutstoreReducer from './nutstore'
import runtimeReducer from './runtime'
import llmReducer from './llm'
import copilotReducer from './copilot'
import preprocessReducer from './preprocess'
import ocrReducer from './ocr'
import inputToolsReducer from './inputTools'
import mcpReducer from './mcp'
import newMessagesReducer from './newMessage'
import messageBlocksReducer from './messageBlock'
import minappsReducer from './minapps'
import selectionStoreReducer from './selectionStore'

// Import services
import { storeSyncService } from '../services/StoreSyncService'

// Combine all reducers
const rootReducer = combineReducers({
  assistants: assistantsReducer,
  settings: settingsReducer,
  shortcuts: shortcutsReducer,
  websearch: websearchReducer,
  memory: memoryReducer,
  backup: backupReducer,
  nutstore: nutstoreReducer,
  runtime: runtimeReducer,
  llm: llmReducer,
  copilot: copilotReducer,
  preprocess: preprocessReducer,
  ocr: ocrReducer,
  inputTools: inputToolsReducer,
  mcp: mcpReducer,
  messages: newMessagesReducer,
  messageBlocks: messageBlocksReducer,
  minapps: minappsReducer,
  selectionStore: selectionStoreReducer
})

// Persist configuration
const persistConfig = {
  key: 'cherry-studio',
  storage,
  version: 123,
  blacklist: ['runtime', 'messages', 'messageBlocks'],
  migrate: (state: any) => {
    // Migration logic if needed
    return Promise.resolve(state)
  }
}

const persistedReducer = persistReducer(persistConfig, rootReducer)

// Configure store
const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) => {
    return getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        ignoredActionsPaths: ['register', 'rehydrate'],
        warnAfter: 128
      },
      immutableCheck: { warnAfter: 128 }
    }).concat(storeSyncService.createMiddleware())
  },
  devTools: process.env.NODE_ENV !== 'production'
})

// Create persistor
const persistor = persistStore(store)

// Export types
export type RootState = ReturnType<typeof rootReducer>
export type AppDispatch = typeof store.dispatch

// Make store available globally for debugging
if (typeof window !== 'undefined') {
  window.store = store
}

export { store, persistor }
