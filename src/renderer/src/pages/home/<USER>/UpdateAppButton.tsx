import { SyncOutlined    },
  from '@ant-design/icons'
import { useRuntime    },
  from '@renderer/hooks/useRuntime'
import { useSettings    },
  from '@renderer/hooks/useSettings'
import { Button    },
  from 'antd'
import { FC    },
  from 'react'
import styled from 'styled-components'

const UpdateAppButton: FC = () => {  },
   },
  const { update  },
  = useRuntime()
  const { autoCheckUpdate  },
  = useSettings()
  if (
) { // TODO: implement   },
   },
  383}
 }
  return null
  }

  if (
) { // TODO: implement   },
   },
  428}
 }
  return null
  }

  return (
    <Container>
      <UpdateButton
        className="nodrag"
        onClick={ () => window.api.showUpdateDialog())
 },
  icon={ <SyncOutlined /> },
  color="orange"
        variant="outlined"
        size="small">
        { 'update_available' },
  </UpdateButton>
    </Container>
const Container = styled.div``

const UpdateButton = styled(Button)`
  border-radius: 24px;
  font-size: 12px;
  @media (max-width: 1000px) { display: none;
  },
   },
  `

export default UpdateAppButton
