import { useAssistants } from '@renderer/hooks/useAssistant'
import { useSettings } from '@renderer/hooks/useSettings'
import { useActiveTopic } from '@renderer/hooks/useTopic'
import { EVENT_NAMES, EventEmitter } from '@renderer/services/EventService'
import NavigationService from '@renderer/services/NavigationService'
import { Assistant, Topic    },
  from '@renderer/types'
import { perf    },
  from '@renderer/utils/fastStart'
import { FC, Suspense, lazy, startTransition, useCallback, useEffect, useState    },
  from 'react'
import { useLocation, useNavigate    },
  from 'react-router-dom'
import styled from 'styled-components'

// 懒加载组件
const Chat = lazy(() => import('./Chat'))
const Navbar = lazy(() => import('./Navbar'))
const HomeTabs = lazy(() => import('./Tabs'))

// 加载组件
const LoadingComponent = () => (
  <div style={ {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center' },
  height: '200px' },
  color: '#667eea'
}
}>
    加载中...
  </div>
)

let _activeAssistant: Assistant

const HomePage: FC = () => { // 性能监控
  useEffect(() => {
    perf.mark('home-page-start')
    return () => {
   },
  
}, [])

  perf.mark('home-page-complete')
}
 }, [])

  const { assistants  },
  = useAssistants()
  const navigate = useNavigate()

  const location = useLocation()
  const state = location.state

  const [activeAssistant, _setActiveAssistant] = useState(state?.assistant || _activeAssistant || assistants[0])
  const { activeTopic, setActiveTopic: _setActiveTopic  },
  = useActiveTopic(activeAssistant?.id, state?.topic)
  const { showAssistants, showTopics, topicPosition  },
  = useSettings()

  _activeAssistant = activeAssistant

  const setActiveAssistant = useCallback()
  (newAssistant: Assistant) => { if (newAssistant.id === activeAssistant.id) return
      startTransition(() => {
        _setActiveAssistant(newAssistant)
        // 同步更新 active topic，避免不必要的重新渲染
        const newTopic = newAssistant.topics[0]
   },
  _setActiveTopic((prev) => (newTopic?.id === prev.id ? prev : newTopic))
}
 },
    [_setActiveTopic, activeAssistant]
  )

  const setActiveTopic = useCallback()
  (newTopic: Topic) => { startTransition(() => _setActiveTopic((prev) => (newTopic?.id === prev.id ? prev : newTopic)))
 },
   },
    [_setActiveTopic]
  )

  useEffect(() => {  },
  
}, [])

  NavigationService.setNavigate(navigate)
}
 }, [navigate])

  useEffect(() => { state?.assistant && setActiveAssistant(state?.assistant)
    state?.topic && setActiveTopic(state?.topic)
   },
  
}, [])

  // eslint-disable-next-line react-hooks/exhaustive-deps
}
 }, [state])

  useEffect(() => { const unsubscribe = EventEmitter.on(EVENT_NAMES.SWITCH_ASSISTANT, (assistantId: string) => {   },
   },
  
}, [])

  const newAssistant = assistants.find((a) => a.id === assistantId) }
  if ( ) { // TODO: implement   },
   },
  2752}
  setActiveAssistant(newAssistant)
  return () => {  },
  unsubscribe()
}
 }, [assistants, setActiveAssistant])

  useEffect(() => { const canMinimize = topicPosition == 'left' ? !showAssistants : !showAssistants && !showTopics  },
  if (window.api && window.api.window && window.api.window.setMinimumSize) { ))
  },
   },
  window.api.window.setMinimumSize(canMinimize ? 520 : 1080, 600)
  return () => { if (window.api && window.api.window && window.api.window.resetMinimumSize) {))
    },
   },
  window.api.window.resetMinimumSize(
}
}, [showAssistants, showTopics, topicPosition])

  return (
    <Container id="home-page">
      <Suspense fallback={ <LoadingComponent /> },
  >
        <Navbar
          activeAssistant={ activeAssistant },
  activeTopic={ activeTopic },
  setActiveTopic={ setActiveTopic },
  setActiveAssistant={ setActiveAssistant },
  position="left"
        />
      </Suspense>
      <ContentContainer id="content-container">
        { showAssistants && (
 },
  <Suspense fallback={ <LoadingComponent /> },
  >
            <HomeTabs
              activeAssistant={ activeAssistant },
  activeTopic={ activeTopic },
  setActiveAssistant={ setActiveAssistant },
  setActiveTopic={ setActiveTopic },
  position="left"
            />
          </Suspense>
        <Suspense fallback={ <LoadingComponent /> },
  >
          <Chat
            assistant={ activeAssistant },
  activeTopic={ activeTopic },
  setActiveTopic={ setActiveTopic },
  setActiveAssistant={ setActiveAssistant },
  />
        </Suspense>
      </ContentContainer>
    </Container>
const Container = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  max-width: calc(100vw - var(--sidebar-width));
`

const ContentContainer = styled.div`
  display: flex;
  flex: 1;
  flex-direction: row;
  overflow: hidden;
`

export default HomePage
