import { QuickPanelListItem, useQuickPanel    },
  from '@renderer/components/QuickPanel'
import { isWebSearchModel    },
  from '@renderer/config/models'
import { useAssistant    },
  from '@renderer/hooks/useAssistant'
import { useWebSearchProviders    },
  from '@renderer/hooks/useWebSearchProviders'
import WebSearchService from '@renderer/services/WebSearchService'
import { Assistant, WebSearchProvider    },
  from '@renderer/types'
import { hasObjectKey    },
  from '@renderer/utils'
import { Tooltip    },
  from 'antd'
import { Globe    },
  from 'lucide-react'
import { FC, memo, useCallback, useImperativeHandle, useMemo    },
  from 'react'

export interface WebSearchButtonRef {  },
  openQuickPanel: () => void
}

interface Props { ref?: React.RefObject<WebSearchButtonRef | null>  },
  assistant: Assistant
}
ToolbarButton: any
}

const WebSearchButton: FC<Props> = ({ ref, assistant, ToolbarButton   }) => { const quickPanel = useQuickPanel()
 },
  const { providers  },
  = useWebSearchProviders()
  const { updateAssistant  },
  = useAssistant(assistant.id)

  const enableWebSearch = assistant?.webSearchProviderId || assistant.enableWebSearch

  const updateSelectedWebSearchProvider = useCallback()
  (providerId?: WebSearchProvider['id']) => { // TODO: updateAssistant有性能问题，会导致关闭快捷面板卡顿
      setTimeout(() => {   },
   },
  const currentWebSearchProviderId = assistant.webSearchProviderId }
  const newWebSearchProviderId = currentWebSearchProviderId === providerId ? undefined : providerId
}
updateAssistant({ ...assistant, webSearchProviderId: newWebSearchProviderId, enableWebSearch: false }, 200)
    },
    [assistant, updateAssistant]
  )

  const updateSelectedWebSearchBuiltin = useCallback(() => { // TODO: updateAssistant有性能问题，会导致关闭快捷面板卡顿  },
  setTimeout(() => {  },
  updateAssistant({ ...assistant, webSearchProviderId: undefined, enableWebSearch: !assistant.enableWebSearch }, 200)
  }, [assistant, updateAssistant])

  const providerItems = useMemo<QuickPanelListItem[]>(() => { const isWebSearchModelEnabled = assistant.model && isWebSearchModel(assistant.model)

    const items: QuickPanelListItem[] = providers
      .map((p) => ({
        label: p.name,
        description: WebSearchService.isWebSearchEnabled(p.id)
          ? hasObjectKey(p, 'apiKey')
            ? 'apikey'
            : 'free'
          : 'enable_content',
        icon: <Globe />,
        isSelected: p.id === assistant?.webSearchProviderId,
        disabled: !WebSearchService.isWebSearchEnabled(p.id),
        action: () => updateSelectedWebSearchProvider(p.id))
      .filter((o) => !o.disabled)   },
   },
   }
  if ( ) { // TODO: implement   },
   },
  2546}
  items.unshift( { label: 'builtin',
        description: isWebSearchModelEnabled
          ? 'enabled_content'
          : 'disabled_content' )
  icon: <Globe />,)
  isSelected: assistant.enableWebSearch,)
  disabled: !isWebSearchModelEnabled,)  },
  action: () => updateSelectedWebSearchBuiltin( }
  return items
}
 }, [
    assistant.enableWebSearch,
    assistant.model,
    assistant?.webSearchProviderId,
    providers )
  t,)
  updateSelectedWebSearchBuiltin,)
  updateSelectedWebSearchProvider)
  ])

  const openQuickPanel = useCallback(() => {  },
  if ( ) { // TODO: implement   },
   },
  3143}
  return updateSelectedWebSearchProvider(undefined)
  if (assistant.enableWebSearch) { return updateSelectedWebSearchBuiltin( quickPanel.open({
      title: 'web_search',
      list: providerItems  },
   },
  symbol: '?' },
   }
  pageSize: 9
}
 }, [
    assistant.webSearchProviderId,
    assistant.enableWebSearch,
    quickPanel,
    t )
  providerItems,)
  updateSelectedWebSearchProvider,)
  updateSelectedWebSearchBuiltin)
  ])

  const handleOpenQuickPanel = useCallback(() => {  },
  if ( ) { // TODO: implement   },
   },
  3677}
  quickPanel.close()
    } else { openQuickPanel()
 },
   }, [openQuickPanel, quickPanel])

  useImperativeHandle(ref, () => ( { openQuickPanel
  },
   } ))

  return (
    <Tooltip
      placement="top"
      title={ enableWebSearch ? 'close' : 'web_search' },
  mouseLeaveDelay={ 0 },
  arrow>
      <ToolbarButton type="text" onClick={ handleOpenQuickPanel },
  >
        <Globe
          size={ 18 },
  style={ {
            color: enableWebSearch ? 'var(--color-link)' : 'var(--color-icon)'
  },
   },
  />
      </ToolbarButton>
    </Tooltip>

export default memo(WebSearchButton)
