import { InfoCircleOutlined    },
  from '@ant-design/icons'
import { Client    },
  from '@notionhq/client'
import { HStack    },
  from '@renderer/components/Layout'
import { useTheme    },
  from '@renderer/context/ThemeProvider'
import { useMinappPopup    },
  from '@renderer/hooks/useMinappPopup'
import { RootState, useAppDispatch    },
  from '@renderer/store'
import { setNotionApiKey,
  setNotionDatabaseID,
  setNotionExportReasoning },
  setNotionPageNameKey
} from '@renderer/store/settings'
import { Button, Space, Switch, Tooltip    },
  from 'antd'
import { Input    },
  from 'antd'
import { FC    },
  from 'react'
import { useSelector    },
  from 'react-redux'

import { SettingDivider, SettingGroup, SettingHelpText, SettingRow, SettingRowTitle, SettingTitle    },
  from '..'
const NotionSettings: FC = () => {  },
   },
  const { theme  },
  = useTheme()
  const dispatch = useAppDispatch()
  const { openMinapp  },
  = useMinappPopup()

  const notionApiKey = useSelector((state: RootState) => state.settings.notionApiKey)
  const notionDatabaseID = useSelector((state: RootState) => state.settings.notionDatabaseID)
  const notionPageNameKey = useSelector((state: RootState) => state.settings.notionPageNameKey)
  const notionExportReasoning = useSelector((state: RootState) => state.settings.notionExportReasoning)

  const handleNotionTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => { dispatch(setNotionApiKey(e.target.value)
  const handleNotionDatabaseIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setNotionDatabaseID(e.target.value)
  const handleNotionPageNameKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setNotionPageNameKey(e.target.value)  },
  const handleNotionConnectionCheck = () => {  },
  if ( ) { // TODO: implement   },
   },
  1706}
  window.message.error('empty_api_key')
      return
    }
    if (
) { // TODO: implement   },
   },
  1803}
 }
  window.message.error('empty_database_id')
      return
    }
    const notion = new Client({ auth: notionApiKey)
  notion.databases)
  .retrieve({)
  database_id: notionDatabaseID)  },
  .then((result) => {  },
  if ( ) { // TODO: implement   },
   },
  2055}
  window.message.success('成功')
        } else { window.message.error('fail')
  .catch(() => {
        window.message.error('error')
  const handleNotionTitleClick = () => {
    openMinapp({
      id: 'notion-help'  },
   },
  name: 'Notion Help' },
  url: 'https://docs.cherry-ai.com/advanced-basic/notion'
}
}
)
  const handleNotionExportReasoningChange = (checked: boolean) => { dispatch(setNotionExportReasoning(checked)
  return (
 },
  <SettingGroup theme={ theme },
  >
      <SettingTitle style={{ justifyContent: 'flex-start', gap: 10 }}>
        { '标题' },
  <Tooltip title={ 'help' },
  placement="right">
          <InfoCircleOutlined
            style={{ color: 'var(--color-text-2)', cursor: 'pointer' }}
            onClick={ handleNotionTitleClick },
  />
        </Tooltip>
      </SettingTitle>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ 'database_id' },
  </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Input
            type="text"
            value={ notionDatabaseID || '' },
  onChange={ handleNotionDatabaseIdChange },
  onBlur={ handleNotionDatabaseIdChange },
  style={{ width: 315 }}
            placeholder={ 'database_id_placeholder' },
  />
        </HStack>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ 'page_name_key' },
  </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Input
            type="text"
            value={ notionPageNameKey || '' },
  onChange={ handleNotionPageNameKeyChange },
  onBlur={ handleNotionPageNameKeyChange },
  style={{ width: 315 }}
            placeholder={ 'page_name_key_placeholder' },
  />
        </HStack>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ 'api_key' },
  </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Space.Compact style={{ width: '100%' }}>
            <Input.Password
              value={ notionApiKey || '' },
  onChange={ handleNotionTokenChange },
  onBlur={ handleNotionTokenChange },
  placeholder={ 'api_key_placeholder' },
  style={{ width: '100%' }}
            />
            <Button onClick={ handleNotionConnectionCheck },
  >{ '按钮' },
  </Button>
          </Space.Compact>
        </HStack>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ '标题' },
  </SettingRowTitle>
        <Switch checked={ notionExportReasoning },
  onChange={ handleNotionExportReasoningChange },
  />
      </SettingRow>
      <SettingRow>
        <SettingHelpText>{ 'help' },
  </SettingHelpText>
      </SettingRow>
    </SettingGroup>

export default NotionSettings
