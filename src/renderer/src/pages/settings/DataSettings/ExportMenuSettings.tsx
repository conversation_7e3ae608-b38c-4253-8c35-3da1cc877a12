import { useTheme    },
  from '@renderer/context/ThemeProvider'
import { RootState, useAppDispatch    },
  from '@renderer/store'
import { setExportMenuOptions    },
  from '@renderer/store/settings'
import { Switch    },
  from 'antd'
import { FC    },
  from 'react'
import { useSelector    },
  from 'react-redux'

import { SettingDivider, SettingGroup, SettingRow, SettingRowTitle, SettingTitle    },
  from '..'

const ExportMenuOptions: FC = () => {  },
   },
  const { theme  },
  = useTheme()
  const dispatch = useAppDispatch()

  const exportMenuOptions = useSelector((state: RootState) => state.settings.exportMenuOptions)

  const handleToggleOption = (option: string, checked: boolean) => { dispatch(
      setExportMenuOptions({
        ...exportMenuOptions,
        [option]: checked  },
  return (
}
<SettingGroup theme={ theme },
  >
      <SettingTitle>{ '标题' },
  </SettingTitle>)
  <SettingDivider />
)
  <SettingRow>)
  <SettingRowTitle>{ 'image' },
  </SettingRowTitle>)
  <Switch checked={ exportMenuOptions.image },
  onChange={ (checked) => handleToggleOption('image', checked) },
  />
      </SettingRow>
      <SettingDivider />

      <SettingRow>
        <SettingRowTitle>{ 'markdown' },
  </SettingRowTitle>
        <Switch checked={ exportMenuOptions.markdown },
  onChange={ (checked) => handleToggleOption('markdown', checked) },
  />
      </SettingRow>
      <SettingDivider />

      <SettingRow>
        <SettingRowTitle>{ 'markdown_reason' },
  </SettingRowTitle>
        <Switch
          checked={ exportMenuOptions.markdown_reason },
  onChange={ (checked) => handleToggleOption('markdown_reason', checked
        />
      </SettingRow>
      <SettingDivider />
)
  <SettingRow>)
   },
  )
  <SettingRowTitle>{ 'notion' },
  </SettingRowTitle>)
  <Switch checked={ exportMenuOptions.notion },
  onChange={ (checked) => handleToggleOption('notion', checked) },
  />
      </SettingRow>
      <SettingDivider />

      <SettingRow>
        <SettingRowTitle>{ 'yuque' },
  </SettingRowTitle>
        <Switch checked={ exportMenuOptions.yuque },
  onChange={ (checked) => handleToggleOption('yuque', checked) },
  />
      </SettingRow>
      <SettingDivider />

      <SettingRow>
        <SettingRowTitle>{ 'joplin' },
  </SettingRowTitle>
        <Switch checked={ exportMenuOptions.joplin },
  onChange={ (checked) => handleToggleOption('joplin', checked) },
  />
      </SettingRow>
      <SettingDivider />

      <SettingRow>
        <SettingRowTitle>{ 'obsidian' },
  </SettingRowTitle>
        <Switch checked={ exportMenuOptions.obsidian },
  onChange={ (checked) => handleToggleOption('obsidian', checked) },
  />
      </SettingRow>
      <SettingDivider />

      <SettingRow>
        <SettingRowTitle>{ 'siyuan' },
  </SettingRowTitle>
        <Switch checked={ exportMenuOptions.siyuan },
  onChange={ (checked) => handleToggleOption('siyuan', checked) },
  />
      </SettingRow>
      <SettingDivider />

      <SettingRow>
        <SettingRowTitle>{ 'docx' },
  </SettingRowTitle>
        <Switch checked={ exportMenuOptions.docx },
  onChange={ (checked) => handleToggleOption('docx', checked) },
  />
      </SettingRow>

      <SettingDivider />

      <SettingRow>
        <SettingRowTitle>{ 'plain_text' },
  </SettingRowTitle>
        <Switch
          checked={ exportMenuOptions.plain_text },
  onChange={ (checked) => handleToggleOption('plain_text', checked)
  />)
  </SettingRow>)
  </SettingGroup>)
 },

export default ExportMenuOptions
