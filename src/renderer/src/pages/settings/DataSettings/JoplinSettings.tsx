import { InfoCircleOutlined    },
  from '@ant-design/icons'
import { HStack    },
  from '@renderer/components/Layout'
import { useTheme    },
  from '@renderer/context/ThemeProvider'
import { useMinappPopup    },
  from '@renderer/hooks/useMinappPopup'
import { RootState, useAppDispatch    },
  from '@renderer/store'
import { setJoplinExportReasoning, setJoplinToken, setJoplinUrl    },
  from '@renderer/store/settings'
import { Button, Space, Switch, Tooltip    },
  from 'antd'
import { Input    },
  from 'antd'
import { FC    },
  from 'react'
import { useSelector    },
  from 'react-redux'

import { SettingDivider, SettingGroup, SettingHelpText, SettingRow, SettingRowTitle, SettingTitle    },
  from '..'

const JoplinSettings: FC = () => {  },
   },
  const { theme  },
  = useTheme()
  const dispatch = useAppDispatch()
  const { openMinapp  },
  = useMinappPopup()

  const joplinToken = useSelector((state: RootState) => state.settings.joplinToken)
  const joplinUrl = useSelector((state: RootState) => state.settings.joplinUrl)
  const joplinExportReasoning = useSelector((state: RootState) => state.settings.joplinExportReasoning)

  const handleJoplinTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => { dispatch(setJoplinToken(e.target.value)
  const handleJoplinUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setJoplinUrl(e.target.value)
  const handleJoplinUrlBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    let url = e.target.value  },
  // 确保URL以/结尾，但只在失去焦点时执行 }
  if (url && !url.endsWith('/')) {  },
  url = `${ url },
  /`
      dispatch(setJoplinUrl(url)
  }

  const handleJoplinConnectionCheck = async () => { try {
   },
  if ( ) { // TODO: implement   },
   },
  1605}
  window.message.error('empty_token')
        return
      }
      if (
) { // TODO: implement   },
   },
  1697}
 }
  window.message.error('empty_url')
        return
      }

      const response = await fetch(`${ joplinUrl },
  notes?limit=1&token=${ joplinToken },
  `)

      const data = await response.json()

      if (
) { // TODO: implement   },
   },
  1918}
 }
  window.message.error('fail')
        return
      }

      window.message.success('成功')
    } catch (e) {  },
   },
  window.message.error('fail')
  }
)
  const handleJoplinHelpClick = () => { openMinapp({
      id: 'joplin-help'  },
   },
  name: 'Joplin Help' },
  url: 'https://joplinapp.org/help/apps/clipper'
}
}
)
  const handleToggleJoplinExportReasoning = (checked: boolean) => { dispatch(setJoplinExportReasoning(checked)
  return (
 },
  <SettingGroup theme={ theme },
  >
      <SettingTitle>{ '标题' },
  </SettingTitle>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ 'url' },
  </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Input
            type="text"
            value={ joplinUrl || '' },
  onChange={ handleJoplinUrlChange },
  onBlur={ handleJoplinUrlBlur },
  style={{ width: 315 }}
            placeholder={ 'url_placeholder' },
  />
        </HStack>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle style={{ display: 'flex', alignItems: 'center' }}>
          <span>{ 'token' },
  </span>
          <Tooltip title={ 'help' },
  placement="left">
            <InfoCircleOutlined
              style={{ color: 'var(--color-text-2)', cursor: 'pointer', marginLeft: 4 }}
              onClick={ handleJoplinHelpClick },
  />
          </Tooltip>
        </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Space.Compact style={{ width: '100%' }}>
            <Input.Password
              value={ joplinToken || '' },
  onChange={ handleJoplinTokenChange },
  onBlur={ handleJoplinTokenChange },
  placeholder={ 'token_placeholder' },
  style={{ width: '100%' }}
            />
            <Button onClick={ handleJoplinConnectionCheck },
  >{ '按钮' },
  </Button>
          </Space.Compact>
        </HStack>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ '标题' },
  </SettingRowTitle>
        <Switch checked={ joplinExportReasoning },
  onChange={ handleToggleJoplinExportReasoning },
  />
      </SettingRow>
      <SettingRow>
        <SettingHelpText>{ 'help' },
  </SettingHelpText>
      </SettingRow>
    </SettingGroup>

export default JoplinSettings
