import { DeleteOutlined, FolderOpenOutlined    },
  from '@ant-design/icons'
import { HStack    },
  from '@renderer/components/Layout'
import { useTheme    },
  from '@renderer/context/ThemeProvider'
import { RootState, useAppDispatch    },
  from '@renderer/store'
import { setForceDollarMathInMarkdown,
  setmarkdownExportPath,
  setShowModelNameInMarkdown,
  setShowModelProviderInMarkdown },
  setUseTopicNamingForMessageTitle
} from '@renderer/store/settings'
import { Button, Switch    },
  from 'antd'
import Input from 'antd/es/input/Input'
import { FC    },
  from 'react'
import { useSelector    },
  from 'react-redux'

import { SettingDivider, SettingGroup, SettingHelpText, SettingRow, SettingRowTitle, SettingTitle    },
  from '..'

const MarkdownExportSettings: FC = () => {  },
   },
  const { theme  },
  = useTheme()
  const dispatch = useAppDispatch()

  const markdownExportPath = useSelector((state: RootState) => state.settings.markdownExportPath)
  const forceDollarMathInMarkdown = useSelector((state: RootState) => state.settings.forceDollarMathInMarkdown)
  const useTopicNamingForMessageTitle = useSelector((state: RootState) => state.settings.useTopicNamingForMessageTitle)
  const showModelNameInExport = useSelector((state: RootState) => state.settings.showModelNameInMarkdown)
  const showModelProviderInMarkdown = useSelector((state: RootState) => state.settings.showModelProviderInMarkdown)

  const handleSelectFolder = async () => { const path = await window.api.file.selectFolder()
   },
  if ( ) { // TODO: implement   },
   },
  1473}
  dispatch(setmarkdownExportPath(path)
  }

  const handleClearPath = () => { dispatch(setmarkdownExportPath(null)
  const handleToggleForceDollarMath = (checked: boolean) => {
    dispatch(setForceDollarMathInMarkdown(checked)
  const handleToggleTopicNaming = (checked: boolean) => {
    dispatch(setUseTopicNamingForMessageTitle(checked)
  const handleToggleShowModelName = (checked: boolean) => {
    dispatch(setShowModelNameInMarkdown(checked)
  const handleToggleShowModelProvider = (checked: boolean) => {   },
   },
  dispatch(setShowModelProviderInMarkdown(checked) }
  return (
}
<SettingGroup theme={ theme },
  >
      <SettingTitle>{ '标题' },
  </SettingTitle>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ 'path' },
  </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Input
            type="text"
            value={ markdownExportPath || '' },
  readOnly
            style={{ width: 250 }}
            placeholder={ 'path_placeholder' },
  suffix={ markdownExportPath ? (
 },
  <DeleteOutlined onClick={ handleClearPath },
  style={{ color: 'var(--color-error)', cursor: 'pointer' }} />
              ) : null
            }
          />
          <Button onClick={ handleSelectFolder },
  icon={ <FolderOpenOutlined /> },
  >
            { 'select' },
  </Button>
        </HStack>
      </SettingRow>
      <SettingRow>
        <SettingHelpText>{ 'help' },
  </SettingHelpText>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ '标题' },
  </SettingRowTitle>
        <Switch checked={ forceDollarMathInMarkdown },
  onChange={ handleToggleForceDollarMath },
  />
      </SettingRow>
      <SettingRow>
        <SettingHelpText>{ 'help' },
  </SettingHelpText>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ '标题' },
  </SettingRowTitle>
        <Switch checked={ useTopicNamingForMessageTitle },
  onChange={ handleToggleTopicNaming },
  />
      </SettingRow>
      <SettingRow>
        <SettingHelpText>{ 'help' },
  </SettingHelpText>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ '标题' },
  </SettingRowTitle>
        <Switch checked={ showModelNameInExport },
  onChange={ handleToggleShowModelName },
  />
      </SettingRow>
      <SettingRow>
        <SettingHelpText>{ 'help' },
  </SettingHelpText>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ '标题' },
  </SettingRowTitle>
        <Switch checked={ showModelProviderInMarkdown },
  onChange={ handleToggleShowModelProvider },
  />
      </SettingRow>
      <SettingRow>
        <SettingHelpText>{ 'help' },
  </SettingHelpText>
      </SettingRow>
    </SettingGroup>

export default MarkdownExportSettings
