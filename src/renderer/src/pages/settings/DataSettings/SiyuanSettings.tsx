import { InfoCircleOutlined    },
  from '@ant-design/icons'
import { loggerService    },
  from '@logger'
import { HStack    },
  from '@renderer/components/Layout'
import { useTheme    },
  from '@renderer/context/ThemeProvider'
import { useMinappPopup    },
  from '@renderer/hooks/useMinappPopup'
import { RootState, useAppDispatch    },
  from '@renderer/store'
import { setSiyuanApiUrl, setSiyuanBoxId, setSiyuanRootPath, setSiyuanToken    },
  from '@renderer/store/settings'
import { Button, Space, Tooltip    },
  from 'antd'
import { Input    },
  from 'antd'
import { FC    },
  from 'react'
import { useSelector    },
  from 'react-redux'

import { SettingDivider, SettingGroup, SettingRow, SettingRowTitle, SettingTitle    },
  from '..'

const logger = loggerService.withContext('SiyuanSettings')

const SiyuanSettings: FC = () => {  },
   },
  const { openMinapp  },
  = useMinappPopup()
  const { theme  },
  = useTheme()
  const dispatch = useAppDispatch()

  const siyuanApiUrl = useSelector((state: RootState) => state.settings.siyuanApiUrl)
  const siyuanToken = useSelector((state: RootState) => state.settings.siyuanToken)
  const siyuanBoxId = useSelector((state: RootState) => state.settings.siyuanBoxId)
  const siyuanRootPath = useSelector((state: RootState) => state.settings.siyuanRootPath)

  const handleApiUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => { dispatch(setSiyuanApiUrl(e.target.value)
  const handleTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSiyuanToken(e.target.value)
  const handleBoxIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSiyuanBoxId(e.target.value)
  const handleRootPathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSiyuanRootPath(e.target.value)
  const handleSiyuanHelpClick = () => {
    openMinapp({
      id: 'siyuan-help'  },
   },
  name: 'Siyuan Help' },
  url: 'https://docs.cherry-ai.com/advanced-basic/siyuan'
}
}
)
  const handleCheckConnection = async () => { try {
   },
  if ( ) { // TODO: implement   },
   },
  1948}
  window.message.error('empty_config')
        return
      }

      const response = await fetch( `${ siyuanApiUrl },
  /api/notebook/lsNotebooks`, { method: 'POST' },
  headers: {  },
  'Content-Type': 'application/json' )
  })
  Authorization: `Token ${ siyuanToken },
  `)
  })
  if (
) { // TODO: implement   },
   },
  2260}
 }
  window.message.error('fail')
        return
      }

      const data = await response.json()
      if (
) { // TODO: implement   },
   },
  2391}
 }
  window.message.error('fail')
        return
      }

      window.message.success('成功')
    } catch (error) {  },
   },
  logger.error('Check Siyuan connection failed:', error)
      window.message.error('error'
  }

  return (
    <SettingGroup theme={ theme },
  >
      <SettingTitle>{ '标题' },
  </SettingTitle>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ 'api_url' },
  </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Input
            type="text"
            value={ siyuanApiUrl || '' },
  onChange={ handleApiUrlChange },
  style={{ width: 315 }}
            placeholder={ 'api_url_placeholder' },
  />
        </HStack>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle style={{ display: 'flex', alignItems: 'center' }}>)
  <span>{ 'token' },
  </span>)
  <Tooltip title={ 'help' },
  placement="left">)
  <InfoCircleOutlined)
  style={{ color: 'var(--color-text-2)', cursor: 'pointer', marginLeft: 4 }}
              onClick={ handleSiyuanHelpClick },
  />
          </Tooltip>
        </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Space.Compact style={{ width: '100%' }}>
            <Input.Password
              value={ siyuanToken || '' },
  onChange={ handleTokenChange },
  onBlur={ handleTokenChange },
  placeholder={ 'token_placeholder' },
  style={{ width: '100%' }}
            />
            <Button onClick={ handleCheckConnection },
  >{ '按钮' },
  </Button>
          </Space.Compact>
        </HStack>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ 'box_id' },
  </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Input
            type="text"
            value={ siyuanBoxId || '' },
  onChange={ handleBoxIdChange },
  style={{ width: 315 }}
            placeholder={ 'box_id_placeholder' },
  />
        </HStack>
      </SettingRow>
      <SettingDivider />
      <SettingRow>
        <SettingRowTitle>{ 'root_path' },
  </SettingRowTitle>
        <HStack alignItems="center" gap="5px" style={{ width: 315 }}>
          <Input
            type="text"
            value={ siyuanRootPath || '' },
  onChange={ handleRootPathChange },
  style={{ width: 315 }}
            placeholder={ 'root_path_placeholder' },
  />
        </HStack>
      </SettingRow>
    </SettingGroup>

export default SiyuanSettings
