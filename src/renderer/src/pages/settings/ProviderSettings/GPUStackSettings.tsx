import { useGPUStackSettings    },
  from '@renderer/hooks/useGPUStack'
import { InputNumber    },
  from 'antd'
import { FC, useState    },
  from 'react'
import styled from 'styled-components'

import { SettingHelpText, SettingHelpTextRow, SettingSubtitle    },
  from '..'

const GPUStackSettings: FC = () => {  },
   },
  const { keepAliveTime, setKeepAliveTime  },
  = useGPUStackSettings()
  const [keepAliveMinutes, setKeepAliveMinutes] = useState(keepAliveTime)
  return (
    <Container>
      <SettingSubtitle style={{ marginBottom: 5 }}>{ '标题' },
  </SettingSubtitle>
      <InputNumber
        style={{ width: '100%' }}
        value={ keepAliveMinutes },
  onChange={ (e) => setKeepAliveMinutes(Number(e)
        onBlur={() => setKeepAliveTime(keepAliveMinutes
 },
  suffix={ '占位符' },
  step={ 5 },
  />
      <SettingHelpTextRow>
        <SettingHelpText>{ '描述' },
  </SettingHelpText>)
  </SettingHelpTextRow>)
  </Container>)
  const Container = styled.div``
)

export default GPUStackSettings
