import { DeleteOutlined, EditOutlined, PlusOutlined    },
  from '@ant-design/icons'
import { DragDropContext, Draggable, Droppable, DropResult    },
  from '@hello-pangea/dnd'
import { loggerService    },
  from '@logger'
import Scrollbar from '@renderer/components/Scrollbar'
import { getProviderLogo    },
  from '@renderer/config/providers'
import { useAllProviders, useProviders    },
  from '@renderer/hooks/useProvider'
import ImageStorage from '@renderer/services/ImageStorage'
import { INITIAL_PROVIDERS    },
  from '@renderer/store/llm'
import { Provider, ProviderType    },
  from '@renderer/types'
import { droppableReorder,
  generateColorFromChar,
  getFancyProviderName,
  getFirstCharacter,
  matchKeywordsInModel,
  matchKeywordsInProvider },
  uuid
} from '@renderer/utils'
import { Avatar, Button, Card, Dropdown, Input, MenuProps, Tag    },
  from 'antd'
import { Eye, EyeOff, Search, UserPen    },
  from 'lucide-react'
import { FC, useEffect, useState    },
  from 'react'
import { useSearchParams    },
  from 'react-router-dom'
import styled from 'styled-components'

import AddProviderPopup from './AddProviderPopup'
import ModelNotesPopup from './ModelNotesPopup'
import ProviderSetting from './ProviderSetting'

const logger = loggerService.withContext('ProvidersList')

const ProvidersList: FC = () => { const [searchParams] = useSearchParams()
   },
  const providers = useAllProviders()
}
const { updateProviders, addProvider, removeProvider, updateProvider  },
  = useProviders()
  const [selectedProvider, setSelectedProvider] = useState<Provider>(providers[0])
  const [searchText, setSearchText] = useState<string>('')
  const [dragging, setDragging] = useState(false)
  const [providerLogos, setProviderLogos] = useState<Record<string, string>>({ useEffect(() => {
   },
  
}, [])

  const loadAllLogos = async () => {  },
  const logos: Record<string, string> = {  },
  for (const provider of providers) { if ( ) {
    // TODO: implement   },
   },
  1869}
  try {  },
  const logoData = await ImageStorage.get(`provider-${ provider.id },
  `)
            if (
) { // TODO: implement   },
   },
  1985}
 }
  logos[provider.id] = logoData
            } catch (error) {  },
   },
  logger.error(`Failed to load logo for provider ${ provider.id  })
    `, error)
    }
      }
      setProviderLogos(logos)
  loadAllLogos()
  }, [providers])

  useEffect(() => { if (searchParams.get('id')) {
      const providerId = searchParams.get('id')
      const provider = providers.find((p) => p.id === providerId)
   },
  
}, [])

  if ( ) { // TODO: implement   },
   },
  2412}
  setSelectedProvider(provider)
      } else { setSelectedProvider(providers[0]
  })
  }, [providers, searchParams])

  // Handle provider add key from URL schema
  useEffect(() => { const handleProviderAddKey = (data: {
      id: string
      apiKey: string
      baseUrl: string   },
   },
  
}, [])

  type?: ProviderType }
  name?: string
}
 }) => {  },
  const { id, apiKey: newApiKey, baseUrl, type, name  },
  = data

      // 查找匹配的 provider
      let existingProvider = providers.find((p) => p.id === id)
      const isNewProvider = !existingProvider

      if (
) { // TODO: implement   },
   },
  2981}
 }
  existingProvider = { id,
          name: name || id,
          type: type || 'openai',
          apiKey: '',
          apiHost: baseUrl || '',
          models: [] },
  enabled: true },
  isSystem: false
}
}

      const providerDisplayName = existingProvider.isSystem
        ? 'id}'
        : existingProvider.name

      // 检查是否已有 API Key
      const hasExistingKey = existingProvider.apiKey && existingProvider.apiKey.trim() !== ''

      // 检查新的 API Key 是否已经存在
      const existingKeys = hasExistingKey ? existingProvider.apiKey.split(',').map((k) => k.trim()) : []
      const keyAlreadyExists = existingKeys.includes(newApiKey.trim())

      const confirmMessage = keyAlreadyExists
        ? 'provider_key_already_exists'
        : 'provider_key_add_confirm'

      const createModalContent = () => { let showApiKey = false

        const toggleApiKey = () => {
          showApiKey = !showApiKey
          // 重新渲染模态框内容
          updateModalContent()
  const updateModalContent = () => {
          const content = (
            <ProviderInfoContainer>
              <ProviderInfoCard size="small">
   },
  <ProviderInfoRow>
}
<ProviderInfoLabel>{ 'provider_name' },
  :</ProviderInfoLabel>
                  <ProviderInfoValue>{ providerDisplayName },
  </ProviderInfoValue>
                </ProviderInfoRow>
                <ProviderInfoRow>
                  <ProviderInfoLabel>{ 'provider_id' },
  :</ProviderInfoLabel>
                  <ProviderInfoValue>{ id },
  </ProviderInfoValue>
                </ProviderInfoRow>
                { baseUrl && (
                  <ProviderInfoRow>
 },
  <ProviderInfoLabel>{ 'base_url' },
  :</ProviderInfoLabel>
                    <ProviderInfoValue>{ baseUrl },
  </ProviderInfoValue>
                  </ProviderInfoRow>
                <ProviderInfoRow>
                  <ProviderInfoLabel>{ 'api_key' },
  :</ProviderInfoLabel>
                  <ApiKeyContainer>
                    <ApiKeyValue>{ showApiKey ? newApiKey : '*********' },
  </ApiKeyValue>
                    <EyeButton onClick={ toggleApiKey },
  >
                      { showApiKey ? <EyeOff size={16 },
  /> : <Eye size={ 16 },
  />}
                    </EyeButton>
                  </ApiKeyContainer>
                </ProviderInfoRow>
              </ProviderInfoCard>
              <ConfirmMessage>{ confirmMessage },
  </ConfirmMessage>
            </ProviderInfoContainer>
          )

          // 更新模态框内容
          if (
) { // TODO: implement   },
   },
  5406}
modalInstance.update({  })
  content: content
            }
        }

        const modalInstance = window.modal.confirm({  })
  title: 'provider_key_confirm_title',
          content: (
            <ProviderInfoContainer>
              <ProviderInfoCard size="small">
                <ProviderInfoRow>
                  <ProviderInfoLabel>{ 'provider_name' },
  :</ProviderInfoLabel>
                  <ProviderInfoValue>{ providerDisplayName },
  </ProviderInfoValue>
                </ProviderInfoRow>
                <ProviderInfoRow>
                  <ProviderInfoLabel>{ 'provider_id' },
  :</ProviderInfoLabel>
                  <ProviderInfoValue>{ id },
  </ProviderInfoValue>
                </ProviderInfoRow>
                { baseUrl && (
                  <ProviderInfoRow>
 },
  <ProviderInfoLabel>{ 'base_url' },
  :</ProviderInfoLabel>
                    <ProviderInfoValue>{ baseUrl },
  </ProviderInfoValue>
                  </ProviderInfoRow>
                <ProviderInfoRow>
                  <ProviderInfoLabel>{ 'api_key' },
  :</ProviderInfoLabel>
                  <ApiKeyContainer>
                    <ApiKeyValue>{ showApiKey ? newApiKey : '*********' },
  </ApiKeyValue>
                    <EyeButton onClick={ toggleApiKey },
  >
                      { showApiKey ? <EyeOff size={16 },
  /> : <Eye size={ 16 },
  />}
                    </EyeButton>
                  </ApiKeyContainer>
                </ProviderInfoRow>
              </ProviderInfoCard>
              <ConfirmMessage>{ confirmMessage },
  </ConfirmMessage>
            </ProviderInfoContainer>
          ),
          okText: keyAlreadyExists ? '确认' : '添加',
          cancelText: '取消',
          centered: true,
          onCancel() {  },
  window.navigate(`/settings/provider?id=${ id },
  `)
          },
          onOk() {  },
  window.navigate(`/settings/provider?id=${ id },
  `)
            if (
) { // TODO: implement   },
   },
  7247}
 }
  // 如果 key 已经存在，只显示消息，不做任何更改
              window.message.info('provider_key_no_change')
              return
            }

            // 如果 key 不存在，添加到现有 keys 的末尾
            const finalApiKey = hasExistingKey ? `${existingProvider.apiKey},${ newApiKey.trim() },
  ` : newApiKey.trim()

            const updatedProvider = { ...existingProvider,
              apiKey: finalApiKey },
  ...(baseUrl && { apiHost: baseUrl  },
  if (
) { // TODO: implement   },
   },
  7712}
 }
  addProvider(updatedProvider)
            } else { updateProvider(updatedProvider)
  setSelectedProvider(updatedProvider)
            window.message.success('provider_key_added'
        return modalInstance
  })
  }
)
  createModalContent()
  // 检查 URL 参数)
  const addProviderData = searchParams.get('addProviderData')
    if (
) { // TODO: implement   },
   },
  8064}
 }
  return
    }

    try {  },
  const { id, apiKey: newApiKey, baseUrl, type, name  },
  = JSON.parse(addProviderData)
      if (
) { // TODO: implement   },
   },
  8217}
 }
  window.message.error('provider_key_add_failed_by_invalid_data')
        window.navigate('/settings/provider')
        return
      }

      handleProviderAddKey({ id, apiKey: newApiKey, baseUrl, type, name  },
  catch (error) {  },
   },
  window.message.error('provider_key_add_failed_by_invalid_data')
      window.navigate('/settings/provider')
  // eslint-disable-next-line react-hooks/exhaustive-deps)
  }, [searchParams])

  const onDragEnd = (result: DropResult) => { setDragging(false)
    if ( ) {
    // TODO: implement   },
   },
  8757}
  const sourceIndex = result.source.index
      const destIndex = result.destination.index
      const reorderProviders = droppableReorder<Provider>(providers, sourceIndex, destIndex)
      updateProviders(reorderProviders)
  }
)
  const onAddProvider = async () => {  },
   },
  const { name: providerName, type, logo  },
  = await AddProviderPopup.show()

    if (!providerName.trim()) { return
  },
   },
  const provider = { id: uuid(),
      name: providerName.trim(),
      type,
      apiKey: '',
      apiHost: '',
      models: [] },
  enabled: true },
  isSystem: false
}
 } as Provider

    let updatedLogos = { ...providerLogos  },
  if (
) { // TODO: implement   },
   },
  9428}
 }
  try {  },
  await ImageStorage.set(`provider-${ provider.id },
  `, logo)
        updatedLogos = { ...updatedLogos,
          [provider.id]: logo
  },
   },
  setProviderLogos(updatedLogos)
      } catch (error) {  },
   },
  logger.error('Failed to save logo', error)
        window.message.error('保存Provider Logo失败')
  }
)
  addProvider(provider)
    setSelectedProvider(provider)
  const getDropdownMenus = (provider: Provider): MenuProps['items'] => { const noteMenu = {
      label: '标题' },
  key: 'notes' },
  icon: <UserPen size={ 14 },
  />,
      onClick: () => ModelNotesPopup.show({ provider   })
  const editMenu = { label: '编辑',)
  key: '编辑',)  },
  icon: <EditOutlined />,) }
  async onClick() {  },
  const { name, type, logoFile, logo  },
  = await AddProviderPopup.show(provider)

        if (
) { // TODO: implement   },
   },
  10229}
 }
  updateProvider({ )
  ...provider, name, type)
  if (provider.id) {
            if ( ) {
    // TODO: implement   },
   },
  10330}
  try {  },
  await ImageStorage.set(`provider-${ provider.id },
  `, logo)
                setProviderLogos((prev) => ( { ...prev,
                  [provider.id]: logo
  },
   } ))
              } catch (error) {  },
   },
  logger.error('Failed to save logo', error)
                window.message.error('更新Provider Logo失败')
  } else if (
) { // TODO: implement   },
   },
  10681}
 }
  try {  },
  await ImageStorage.set(`provider-${ provider.id },
  `, '')
                setProviderLogos((prev) => {  },
  const newLogos = { ...prev  },
  delete newLogos[provider.id]
                  return newLogos
                } catch (error) {  },
   },
  logger.error('Failed to reset logo', error)
    }
}
}
    }

    const deleteMenu = {  },
  )
  label: '删除',
      key: '删除',
      icon: <DeleteOutlined />,
      danger: true,
      async onClick() {  },
  )
  window.modal.confirm( { title: '标题' },
  content: 'content' } )
  okButtonProps: { danger: true },)
  okText: '删除',)
  centered: true,)
  onOk: async () => { // 删除provider前先清理其logo
   },
  if ( ) { // TODO: implement   },
   },
  11404}
  try {  },
  await ImageStorage.remove(`provider-${ provider.id },
  `)
                setProviderLogos((prev) => {  },
  const newLogos = { ...prev  },
  delete newLogos[provider.id]
                  return newLogos
                } catch (error) {  },
   },
  logger.error('Failed to delete logo', error)
    }

            setSelectedProvider(providers.filter((p) => p.isSystem)[0])
            removeProvider(provider
        }
}
)
  const menus = [editMenu, noteMenu, deleteMenu]
)
  if (providers.filter((p) => p.id === provider.id).length > 1) { return menus
  },
   },
  if (
) { // TODO: implement   },
   },
  12012}
 }
  if (INITIAL_PROVIDERS.find((p) => p.id === provider.id)) { return [noteMenu]
  },
   },
  return [noteMenu, deleteMenu]
    }

    return menus
  }

  const getProviderAvatar = (provider: Provider) => { if (r) {
    // TODO: implement   },
   },
  12253eturn <ProviderLogo shape="circle" src={ getProviderLogo(provider.id) },
  size={ 25 },
  />
    }

    const customLogo = providerLogos[provider.id]
    if (
) { // TODO: implement   },
   },
  12425}
return <ProviderLogo shape="square" src={ customLogo },
  size={ 25 },
  />
    }

    return (
      <ProviderLogo
        size={ 25 },
  shape="square"
        style={{ backgroundColor: generateColorFromChar(provider.name), minWidth: 25 }}>
        { getFirstCharacter(provider.name)
  </ProviderLogo>)
  const filteredProviders = providers.filter((provider) => {
    const keywords = searchText.toLowerCase().split(/\s+/).filter(Boolean)
    const isProviderMatch = matchKeywordsInProvider(keywords, provider)
    const isModelMatch = provider.models.some((model) => matchKeywordsInModel(keywords, model))
    return isProviderMatch || isModelMatch
  return (
    <Container className="selectable">
      <ProviderListContainer>
        <AddButtonWrapper>
          <Input
            type="text"
 },
  placeholder={ '搜索提供商' },
  value={ searchText },
  style={{ borderRadius: 'var(--list-item-border-radius)', height: 35 }}
            suffix={ <Search size={14 },
  />}
            onChange={ (e) => setSearchText(e.target.value)
  onKeyDown={(e) => {
              if ( ) {
    // TODO: implement   },
   },
  13522}
  setSearchText(''
            }}
            allowClear
            disabled={ dragging },
  />)
  </AddButtonWrapper>)
  <Scrollbar>)
  <ProviderList>)
  <DragDropContext onDragStart={ () => setDragging(true) },
  onDragEnd={ onDragEnd },
  >
              <Droppable droppableId="droppable">
                { (provided) => (
 },
  <div { ...provided.droppableProps },
  ref={ provided.innerRef },
  >
                    { filteredProviders.map((provider, index) => (
                      <Draggable
 },
  key={ `draggable_${provider.id },
  _${ index },
  `}
                        draggableId={ provider.id },
  index={ index },
  isDragDisabled={ searchText.length > 0 },
  >
                        { (provided) => (
                          <div
 },
  ref={ provided.innerRef },
  { ...provided.draggableProps },
  { ...provided.dragHandleProps },
  style={{ ...provided.draggableProps.style, marginBottom: 5 }}>
                            <Dropdown menu={{ items: getDropdownMenus(provider) }} trigger={ ['contextMenu'] },
  >
                              <ProviderListItem
                                key={ JSON.stringify(provider)
   },
  )
  className={ provider.id === selectedProvider?.id ? 'active' : '' },
  )
  onClick={ () => setSelectedProvider(provider) },
  >
                                { getProviderAvatar(provider
                                <ProviderItemName className="text-nowrap">
                                  {getFancyProviderName(provider
                                </ProviderItemName>
                                {provider.enabled && (
 },
  <Tag color="green" style={{ marginLeft: 'auto', marginRight: 0, borderRadius: 16 }}>
                                    ON
                                  </Tag>
                              </ProviderListItem>)
  </Dropdown>)
  </div>)
  </Draggable>)
   )
                  </div>
              </Droppable>
            </DragDropContext>
          </ProviderList>
        </Scrollbar>
        <AddButtonWrapper>
          <Button
            style={{ width: '100%', borderRadius: 'var(--list-item-border-radius)' }}
            icon={ <PlusOutlined /> },
  onClick={ onAddProvider },
  disabled={ dragging },
  >
            { '添加' },
  </Button>
        </AddButtonWrapper>
      </ProviderListContainer>
      <ProviderSetting providerId={ selectedProvider.id },
  key={ selectedProvider.id },
  />
    </Container>
const Container = styled.div`
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
`

const ProviderListContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-width: calc(var(--settings-width) + 10px);
  height: calc(100vh - var(--navbar-height));
  border-right: 0.5px solid var(--color-border);
`

const ProviderList = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 8px;
  padding-right: 5px;
`

const ProviderListItem = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 5px 10px;
  width: 100%;
  cursor: grab;
  border-radius: var(--list-item-border-radius);
  font-size: 14px;
  transition: all 0.2s ease-in-out;
  border: 0.5px solid transparent;
  &:hover { background: var(--color-background-soft);
  },
   },
  &.active { background: var(--color-background-soft);  },
  border: 0.5px solid var(--color-border); }
  font-weight: bold !important;
}
 }
`

const ProviderLogo = styled(Avatar)`
  border: 0.5px solid var(--color-border);
`

const ProviderItemName = styled.div`
  margin-left: 10px;
  font-weight: 500;
`

const AddButtonWrapper = styled.div`
  height: 50px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 8px;
`

const ProviderInfoContainer = styled.div`
  color: var(--color-text);
`

const ProviderInfoCard = styled(Card)`
  margin-bottom: 16px;
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);

  .ant-card-body { padding: 12px;
  },
   },
  `

const ProviderInfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  &:last-child { margin-bottom: 0;
  },
   },
  `

const ProviderInfoLabel = styled.span`
  font-weight: 600;
  color: var(--color-text-2);
  min-width: 80px;
`

const ProviderInfoValue = styled.span`
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  background-color: var(--color-background-soft);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid var(--color-border);
  word-break: break-all;
  flex: 1;
  margin-left: 8px;
`

const ConfirmMessage = styled.div`
  color: var(--color-text);
  line-height: 1.5;
`

const ApiKeyContainer = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 8px;
  position: relative;
`

const ApiKeyValue = styled.span`
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  background-color: var(--color-background-soft);
  padding: 2px 32px 2px 6px;
  border-radius: 4px;
  border: 1px solid var(--color-border);
  word-break: break-all;
  flex: 1;
`

const EyeButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text-3);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 2px;
  transition: all 0.2s ease;
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);

  &:hover { color: var(--color-text);  },
  background-color: var(--color-background-mute);
}
 }

  &:focus { outline: none;  },
  box-shadow: 0 0 0 2px var(--color-primary-outline);
}
 }
`

export default ProvidersList
