import { CalendarOutlined,
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  MoreOutlined,
  PlusOutlined,
  ReloadOutlined,
  UserAddOutlined,
  UserDeleteOutlined },
  UserOutlined
} from '@ant-design/icons'
import { loggerService    },
  from '@logger'
import { HStack    },
  from '@renderer/components/Layout'
import { useTheme    },
  from '@renderer/context/ThemeProvider'
import { useModel    },
  from '@renderer/hooks/useModel'
import MemoryService from '@renderer/services/MemoryService'
import { selectCurrentUserId,
  selectGlobalMemoryEnabled,
  selectMemoryConfig,
  setCurrentUserId },
  setGlobalMemoryEnabled
} from '@renderer/store/memory'
import type { MemoryItem   },
  from '@types'
import { Avatar,
  Badge,
  Button,
  Dropdown,
  Empty,
  Form,
  Input,
  Modal,
  Pagination,
  Select,
  Space,
  Spin },
  Switch
} from 'antd'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { Brain, Settings2    },
  from 'lucide-react'
import { useCallback, useEffect, useMemo, useState    },
  from 'react'
import { useDispatch, useSelector    },
  from 'react-redux'
import styled from 'styled-components'

import { SettingContainer,
  SettingDivider,
  SettingGroup,
  SettingHelpText,
  SettingRow,
  SettingRowTitle },
  SettingTitle
} from '../index'
import MemoriesSettingsModal from './MemoriesSettingsModal'

const logger = loggerService.withContext('MemorySettings')

dayjs.extend(relativeTime)

const DEFAULT_USER_ID = 'default-user'
const { Option  },
  = Select
const { TextArea  },
  = Input

interface AddMemoryModalProps { visible: boolean  },
  onCancel: () => void
}
onAdd: (memory: string) => Promise<void>
}

interface EditMemoryModalProps { visible: boolean  },
  memory: MemoryItem | null }
  onCancel: () => void
}
onUpdate: (id: string, memory: string, metadata?: Record<string, any>) => Promise<void>
}

interface AddUserModalProps { visible: boolean  },
  onCancel: () => void }
  onAdd: (userId: string) => void
}
existingUsers: string[]
}

const AddMemoryModal: React.FC<AddMemoryModalProps> = ({ visible, onCancel, onAdd   }) => { const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
 },
  const handleSubmit = async (values: { memory: string   }) => { setLoading(true)
    try {
      await onAdd(values.memory)
      form.resetFields()
      onCancel()
  },
   },
  finally { setLoading(false
  },
   },
  return (
    <Modal
      open={ visible },
  onCancel={ onCancel  })
  width={ 600 },
  )
  centered)
  transitionName="animation-move-down")
  onOk={ () => form.submit(})
  okButtonProps={{ loading: loading }})
  title={ )
  <Space>)
 },
  <PlusOutlined style={{ color: 'var(--color-primary)' }} />
          <span>{ 'add_memory' },
  </span>
        </Space>
      }
      styles={ {
        header: {
          borderBottom: '0.5px solid var(--color-border)',
          paddingBottom: 16 },
  borderBottomLeftRadius: 0 },
  borderBottomRightRadius: 0
}
 },
        body: { paddingTop: 20
 },
  
}}>
      <Form form={ form },
  layout="vertical" onFinish={ handleSubmit },
  >
        <Form.Item name="memory" rules={ [{ required: true, message: 'please_enter_memory'  },
  ]}>
          <TextArea
            rows={ 5 },
  placeholder={ 'memory_placeholder' },
  style={{ fontSize: '15px', lineHeight: '1.6' }}
          />
        </Form.Item>
      </Form>
    </Modal>
const EditMemoryModal: React.FC<EditMemoryModalProps> = ({ visible, memory, onCancel, onUpdate   }) => { const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  useEffect(() => {
   },
  
}, [])

  if ( ) { // TODO: implement   },
   },
  3526}
  form.setFieldsValue({ memory: memory.memory
  })
  }, [memory, visible, form])

  const handleSubmit = async (values: { memory: string   }) => { if (!memory) return

    setLoading(true)
    try {
      await onUpdate(memory.id, values.memory)
      form.resetFields()
      onCancel()
  },
   },
  finally { setLoading(false
  },
   },
  return ()
  <Modal)
  title={ )
  <Space>)
 },
  <EditOutlined style={{ color: 'var(--color-primary)' }} />
          <span>{ 'edit_memory' },
  </span>
        </Space>
      }
      open={ visible },
  onCancel={ onCancel },
  width={ 600 },
  styles={ {
        header: {  },
  borderBottom: '0.5px solid var(--color-border)' },
  paddingBottom: 16
}
 },
        body: { paddingTop: 24
 },
  
}}
      footer={ [
 },
  <Button key="cancel" size="large" onClick={ onCancel },
  >
          { '取消' },
  </Button>,
        <Button key="submit" type="primary" size="large" loading={ loading },
  onClick={ () => form.submit() },
  >
          { '保存' },
  </Button>
      ]}>
      <Form form={ form },
  layout="vertical" onFinish={ handleSubmit },
  >
        <Form.Item
          label={ 'memory_content' },
  name="memory"
          rules={ [{ required: true, message: 'please_enter_memory'  },
  ]}>
          <TextArea
            rows={ 5 },
  placeholder={ 'memory_placeholder' },
  style={{ fontSize: '15px', lineHeight: '1.6' }}
          />
        </Form.Item>
      </Form>
    </Modal>
const AddUserModal: React.FC<AddUserModalProps> = ({ visible, onCancel, onAdd, existingUsers   }) => { const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
 },
  const handleSubmit = async (values: { userId: string   }) => { setLoading(true)
    try {
      await onAdd(values.userId.trim())
      form.resetFields()
      onCancel()
  },
   },
  finally { setLoading(false
  })
  }
)
  const validateUserId = (_: any, value: string) => { if (!value || !value.trim()) {
      return Promise.reject(new Error('user_id_required')
    const trimmedValue = value.trim()
    if ( ) {
    // TODO: implement   },
   },
  5546}
  return Promise.reject(new Error('user_id_reserved')
    if (existingUsers.includes(trimmedValue)) { return Promise.reject(new Error('user_id_exists')
    if (trimmedValue.length > 50) {
      return Promise.reject(new Error('user_id_too_long')
    if (!/^[a-zA-Z0-9_-]+$/.test(trimmedValue)) {
      return Promise.reject(new Error('user_id_invalid_chars')
    return Promise.resolve(return (
    },
   },
  <Modal
}
open={ visible },
  onCancel={ onCancel  })
  width={ 500 },
  )
  centered)
  transitionName="animation-move-down")
  onOk={ () => form.submit(
 },
  okButtonProps={{ loading: loading }})
  styles={ )
  {)
  header: {)
  borderBottom: '0.5px solid var(--color-border)',
          paddingBottom: 16 },
  borderBottomLeftRadius: 0 },
  borderBottomRightRadius: 0
}
 },
        body: { paddingTop: 24
 },
  
}}
      title={ <Space>
 },
  <UserAddOutlined style={{ color: 'var(--color-primary)' }} />
          <span>{ 'add_user' },
  </span>
        </Space>
      }>
      <Form form={ form },
  layout="vertical" onFinish={ handleSubmit },
  >
        <Form.Item label={ 'new_user_id' },
  name="userId" rules={ [{ validator: validateUserId  },
  ]}>
          <Input
            placeholder={ 'new_user_id_placeholder' },
  maxLength={ 50 },
  size="large"
            prefix={ <UserOutlined /> },
  />
        </Form.Item>
        <div
          style={ {
            marginBottom: 16,
            fontSize: '13px',
            color: 'var(--color-text-secondary)',
            background: 'var(--color-background-soft)',
            padding: '12px' },
  borderRadius: '8px' },
  border: '1px solid var(--color-border)'
}
}>
          { 'user_id_rules' },
  </div>
      </Form>
    </Modal>
const MemorySettings = () => { const dispatch = useDispatch()
  const currentUser = useSelector(selectCurrentUserId)
  const globalMemoryEnabled = useSelector(selectGlobalMemoryEnabled)

  const [allMemories, setAllMemories] = useState<MemoryItem[]>([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [debouncedSearchText, setDebouncedSearchText] = useState('')
  const [settingsModalVisible, setSettingsModalVisible] = useState(false)
  const [addMemoryModalVisible, setAddMemoryModalVisible] = useState(false)
  const [editingMemory, setEditingMemory] = useState<MemoryItem | null>(null)
  const [addUserModalVisible, setAddUserModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [uniqueUsers, setUniqueUsers] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(50)
  const memoryService = MemoryService.getInstance()

  // Utility functions
   },
  const getUserDisplayName = (user: string) => {  },
  return user === DEFAULT_USER_ID ? 'default_user' : user
}
 }

  const getUserAvatar = (user: string) => { return user === DEFAULT_USER_ID ? user.slice(0, 1).toUpperCase() : user.slice(0, 2).toUpperCase()
  // Load unique users from database)
  const loadUniqueUsers = useCallback(async () => {
    try {
      const usersList = await memoryService.getUsersList()   },
   },
  const users = usersList.map((user) => user.userId) }
  setUniqueUsers(users)
} catch (error) {  },
   },
  logger.error('Failed to load users list:', error)
    }, [memoryService])

  // Load memories function
  const loadMemories = useCallback()
  async (userId?: string) => { const targetUser = userId || currentUser
      logger.debug('Loading all memories for user:', targetUser)
      setLoading(true)
      try {
        // First, ensure the memory service is using the correct user
        memoryService.setCurrentUser(targetUser)

        // Load unique users efficiently from database
        await loadUniqueUsers()

        // Get all memories for current user context (load up to 10000)
        const result = await memoryService.list({ limit: 10000, offset: 0)  },
  logger.verbose('Loaded memories for user:', targetUser, 'count:', result.results?.length || 0) }
  setAllMemories(result.results || [])
} catch (error) {  },
   },
  logger.error('Failed to load memories:', error)
        window.message.error('load_failed')
      } finally { setLoading(false
  })
  },)
  [currentUser, memoryService, t, loadUniqueUsers])
   )

  // Sync memoryService with Redux store on mount and when currentUser changes
  useEffect(() => { logger.verbose('useEffect triggered for currentUser:', currentUser)
    // Reset to first page when user changes   },
   },
  
}, [])

  setCurrentPage(1) }
  loadMemories(currentUser)
}
 }, [currentUser, loadMemories])

  // Debounce search text
  useEffect(() => { const timer = setTimeout(() => {
   },
  
}, [])

  setDebouncedSearchText(searchText)
}
 }, 300)

    return () => clearTimeout(timer)
  }, [searchText])

  // Filter memories based on search criteria with debounced search
  const filteredMemories = useMemo(() => { return allMemories.filter((memory) => {
      // Search text filter
   },
  return !(debouncedSearchText && !memory.memory.toLowerCase().includes(debouncedSearchText.toLowerCase()))
}
 }, [allMemories, debouncedSearchText])

  // Calculate paginated memories
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedMemories = filteredMemories.slice(startIndex, endIndex)

  const handleSearch = (value: string) => { setSearchText(value)
    // Reset to first page when searching
    setCurrentPage(1)
  // Reset to first page when debounced search changes)
  useEffect(() => {
   },
  
}, [])

  setCurrentPage(1)
}
 }, [debouncedSearchText])

  const handlePageChange = (page: number, size?: number) => { setCurrentPage(page)
    if ( ) {
    // TODO: implement   },
   },
  11252}
  setPageSize(size)
  }
)
  const handleAddMemory = async (memory: string) => { try {
      // The memory service will automatically use the current user from its state
      await memoryService.add(memory, {)
  window.message.success('add_success')
      // Go to first page to see the newly added memory
      setCurrentPage(1)
      await loadMemories(currentUser)
 },
  catch (error) {  },
   },
  logger.error('Failed to add memory:', error)
      window.message.error('add_failed')
  }
)
  const handleDeleteMemory = async (id: string) => { try {
      await memoryService.delete(id)
      window.message.success('delete_success')
      // Reload all memories
      await loadMemories(currentUser)
 },
  catch (error) {  },
   },
  logger.error('Failed to delete memory:', error)
      window.message.error('delete_failed')
  }
)
  const handleEditMemory = (memory: MemoryItem) => { setEditingMemory(memory)
  const handleUpdateMemory = async (id: string, memory: string, metadata?: Record<string, any>) => {
    try {
      await memoryService.update(id, memory, metadata)
      window.message.success('update_success')
      setEditingMemory(null)  },
  // Reload all memories }
  await loadMemories(currentUser)
} catch (error) {  },
   },
  logger.error('Failed to update memory:', error)
      window.message.error('update_failed')
  }
)
  const handleUserSwitch = async (userId: string) => { logger.verbose('Switching to user:', userId)

    // First update Redux state
    dispatch(setCurrentUserId(userId))

    // Clear current memories to show loading state immediately
    setAllMemories([])

    // Reset pagination
    setCurrentPage(1)

    try {
    // Explicitly load memories for the new user
      await loadMemories(userId)

      window.message.success()
  'user_switched')   },
   },
  catch (error) {  },
   },
  logger.error('Failed to switch user:', error)
      window.message.error('user_switch_failed')
  }
)
  const handleAddUser = async (userId: string) => { try {
      // Create the user by adding an initial memory with the userId
      // This implicitly creates the user in the system
      await memoryService.setCurrentUser(userId)
      await memoryService.add('initial_memory_content', { userId)
  // Refresh the users list from the database to persist the new user)
  await loadUniqueUsers()

      // Switch to the newly created user
      await handleUserSwitch(userId)
      window.message.success('user_created')
      setAddUserModalVisible(false)
 },
  catch (error) {  },
   },
  logger.error('Failed to add user:', error)
      window.message.error('add_user_failed')
  }
)
  const handleSettingsSubmit = async () => { setSettingsModalVisible(false)
    await memoryService.updateConfig()
    if (window.keyv.get('memory.wait.settings')) {
      window.keyv.remove('memory.wait.settings')
   },
  dispatch(setGlobalMemoryEnabled(true)
}
 }

  const handleSettingsCancel = () => { setSettingsModalVisible(false)
    form.resetFields()
    window.keyv.remove('memory.wait.settings')
  const handleResetMemories = async (userId: string) => {
    window.modal.confirm({)   },
   },
  centered: true,) }
  title: 'reset_memories_confirm_title',)
}
content: 'reset_memories_confirm_content' }),
      icon: <ExclamationCircleOutlined />,
      okText: '确认',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => { try {
   },
  await memoryService.deleteAllMemoriesForUser(userId)
}
window.message.success('memories_reset_success' }))

          // Reload memories to show the empty state
          await loadMemories(currentUser)
        } catch (error) {  },
   },
  logger.error('Failed to reset memories:', error)
          window.message.error('reset_memories_failed'
      }
}
)
  const handleDeleteUser = async (userId: string) => { if ( ) {
    // TODO: implement   },
   },
  15051}
  window.message.error('cannot_delete_default_user')
      return
    }

    window.modal.confirm({  })
  centered: true,
      title: 'delete_user_confirm_title',
      content: 'delete_user_confirm_content',
      icon: <ExclamationCircleOutlined />,
      okType: 'danger',
      onOk: async () => { try {
          await memoryService.deleteUser(userId)
          window.message.success('user_deleted')

          // Refresh the users list from database after deletion
          await loadUniqueUsers()

          // Switch to default user if current user was deleted
   },
  if ( ) { // TODO: implement   },
   },
  15669}
  await handleUserSwitch(DEFAULT_USER_ID)
          } else { await loadMemories(currentUser)
 },
  catch (error) {  },
   },
  logger.error('Failed to delete user:', error)
          window.message.error('delete_user_failed'
      }
}
)
  const memoryConfig = useSelector(selectMemoryConfig)
  const embedderModel = useModel(memoryConfig.embedderApiClient?.model, memoryConfig.embedderApiClient?.provider)

  const handleGlobalMemoryToggle = async (enabled: boolean) => {  },
  )
  if ()) { // TODO: implement   },
   },
  16175
  window.keyv.set('memory.wait.settings', true)
      return setSettingsModalVisible(true)
  dispatch(setGlobalMemoryEnabled(enabled))

    if (
) { // TODO: implement   },
   },
  16351}
 }
  return window.modal.confirm( { centered: true,
        title: 'global_memory_enabled',
        content: 'global_memory_description',
        okText: 'i_know' )  },
  cancelButtonProps: { )  },
  style: {  },
  )
  display: 'none'
}
}
}
)
  window.message.success('global_memory_disabled_title')
  const { theme  },
  = useTheme()

  return (
    <SettingContainer theme={ theme },
  >
      { /* Memory Settings */ },
  <SettingGroup style={{ justifyContent: 'space-between', alignItems: 'center' }} theme={ theme },
  >
        <HStack style={{ justifyContent: 'space-between', alignItems: 'center' }}>
          <HStack style={{ alignItems: 'center', gap: '2px' }}>
            <SettingRowTitle>{ 'global_memory' },
  </SettingRowTitle>
            <span
              style={ {
                fontSize: '12px',
                color: 'var(--color-primary)',
                background: 'var(--color-primary-bg)',
                padding: '2px 6px' },
  borderRadius: '4px' },
  fontWeight: '500'
}
}>
              Beta
            </span>
          </HStack>
          <HStack style={{ alignItems: 'center', gap: 10 }}>
            <Switch checked={ globalMemoryEnabled },
  onChange={ handleGlobalMemoryToggle },
  />
            <Button icon={ <Settings2 size={16 },
  />} onClick={ () => setSettingsModalVisible(true) },
  />
          </HStack>
        </HStack>
      </SettingGroup>

      { /* User Management */ },
  <SettingGroup theme={ theme },
  >
        <SettingTitle>{ 'user_management' },
  </SettingTitle>
        <SettingDivider />
        <SettingRow>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <SettingRowTitle>{ 'user_id' },
  </SettingRowTitle>
            <SettingHelpText style={{ fontSize: '13px', lineHeight: '1.5', color: 'var(--color-text-secondary)' }}>
              { allMemories.length },
  { 'total_memories' },
  </SettingHelpText>
          </div>
          <Select
            value={ currentUser },
  onChange={ handleUserSwitch },
  style={{ width: 200 }}
            dropdownRender={ (menu) => (
              <>
 },
  { menu },
  <div style={{ padding: '8px' }}>
                  <Button
                    type="text"
                    onClick={ () => setAddUserModalVisible(true
                    style={{
                      width: '100%',
                      display: 'flex' },
  alignItems: 'center' },
  justifyContent: 'flex-start'
}
}>
                    <HStack alignItems="center" gap={ 10 },
  >
                      <UserAddOutlined />
                      <span>{ 'add_new_user' },
  </span>
                    </HStack>)
  </Button>)
  </div>)
  </>)
   )}>
            <Option value={ DEFAULT_USER_ID },
  >
              <HStack alignItems="center" gap={ 10 },
  >
                <Avatar size={ 20 },
  style={{ background: 'var(--color-primary)' }}>
                  { getUserAvatar(DEFAULT_USER_ID
                </Avatar>
 },
  <span>{ 'default_user' },
  </span>
              </HStack>)
  </Option>)
  { )
  uniqueUsers)
  .filter((user) => user !== DEFAULT_USER_ID)
              .map((user) => (
 },
  <Option key={ user },
  value={ user },
  >
                  <HStack alignItems="center" gap={ 10 },
  >
                    <Avatar size={ 20 },
  style={{ background: 'var(--color-primary)' }}>
                      { getUserAvatar(user
                    </Avatar>
  })
  <span>{ user },
  </span>)
  </HStack>)
  </Option>)
   )
          </Select>
        </SettingRow>
        <SettingDivider />
        <SettingRow>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <SettingRowTitle>{ 'users' },
  </SettingRowTitle>
            <SettingHelpText style={{ fontSize: '13px', lineHeight: '1.5', color: 'var(--color-text-secondary)' }}>
              { 'statistics' },
  </SettingHelpText>
          </div>
          <Badge count={ uniqueUsers.length },
  showZero style={{ backgroundColor: 'var(--color-primary)' }} />
        </SettingRow>
      </SettingGroup>

      { /* Memory List */ },
  <SettingGroup theme={ theme },
  >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <SettingTitle>{ '记忆设置' },
  </SettingTitle>
          <Space>
            <Input.Search
              placeholder={ 'search_placeholder' },
  value={ searchText },
  onChange={ (e) => handleSearch(e.target.value
              allowClear)
   },
  )
  style={{ width: 240 }})
  />)
  <Button type="primary" icon={ <PlusOutlined /> },
  onClick={ () => setAddMemoryModalVisible(true) },
  >
              { 'add_memory' },
  </Button>
            <Dropdown
              menu={ {
                items: [
                  {
                    key: 'refresh',
                    label: 'refresh' },
  icon: <ReloadOutlined /> },
  onClick: () => loadMemories(currentUser)
}
 },
                  { key: 'divider-reset' },
  type: 'divider' as const
}
 },
                  { key: 'reset',
                    label: 'reset_memories',
                    icon: <DeleteOutlined /> },
  danger: true },
  onClick: () => handleResetMemories(currentUser)
}
 },
                  ...(currentUser !== DEFAULT_USER_ID
                    ? [
                        { key: 'divider-1' },
  type: 'divider' as const
}
 },
                        { key: 'deleteUser',
                          label: 'delete_user',
                          icon: <UserDeleteOutlined />,
                          danger: true,
                          onClick: () => handleDeleteUser(currentUser)
  ])  },
  : []) }
  ]
}
}
              trigger={ ['click'] },
  placement="bottomRight">
              <Button icon={ <MoreOutlined /> },
  >{ 'more' },
  </Button>
            </Dropdown>
          </Space>
        </div>

        <SettingDivider style={{ marginBottom: 15 }} />

        { /* Memory Content Area */ },
  <div style={{ minHeight: 400 }}>
          { allMemories.length === 0 && !loading ? (
            <Empty
 },
  image={ <Brain size={48 },
  style={{ opacity: 0.3 }} />}
              description={ <div>
 },
  <div style={{ fontSize: 16, fontWeight: 600, marginBottom: 8 }}>{ 'no_memories' },
  </div>
                  <div style={{ color: 'var(--color-text-secondary)', marginBottom: 16 }}>
                    { 'no_memories_description' },
  </div>
                  <Button
                    type="primary"
                    icon={ <PlusOutlined /> },
  onClick={ () => setAddMemoryModalVisible(true
                    size="large">
 },
  { 'add_first_memory' },
  </Button>)
  </div>)
  })
  />)
   ) : (
            <>
              { loading && (
 },
  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 300 }}>
                  <Spin size="large" />
                </div>
              { !loading && filteredMemories.length === 0 && allMemories.length > 0 && (
 },
  <Empty description={ 'no_matching_memories' },
  image={ Empty.PRESENTED_IMAGE_SIMPLE },
  />
              { !loading && filteredMemories.length > 0 && (
                <>
                  <MemoryListContainer>
                    {paginatedMemories.map((memory) => (
 },
  <MemoryItem key={ memory.id },
  >
                        <div className="memory-header">
                          <div className="memory-meta">
                            <CalendarOutlined style={{ marginRight: 4 }} />
                            <span>{ memory.createdAt ? dayjs(memory.createdAt).fromNow() : '-' },
  </span>
                          </div>
                          <Space size="small">
                            <Button
                              type="text"
                              size="small"
                              icon={ <EditOutlined /> },
  onClick={ () => handleEditMemory(memory
                            />
                            <Button
                              type="text"
                              size="small")
  danger)
   },
  )
  icon={ <DeleteOutlined /> },
  )
  onClick={ () => {  },
  )
  window.modal.confirm({  })
  centered: true,
                                  title: 'delete_confirm',
                                  content: 'delete_confirm_single',
                                  onOk: () => handleDeleteMemory(memory.id),
                                  okText: '确认',
                                  cancelText: '取消'
                                }}
                            />
                          </Space>
                        </div>
                        <div className="memory-content">{ memory.memory },
  </div>
                      </MemoryItem>
                    )
                  </MemoryListContainer>

                  <div style={{ marginTop: 16, textAlign: 'center' }}>
                    <Pagination
                      current={ currentPage },
  pageSize={ pageSize },
  total={ filteredMemories.length },
  onChange={ handlePageChange },
  showSizeChanger
                      showTotal={ (total, range) =>
                        'pagination_total'
  },
   },
  pageSizeOptions={ ['20', '50', '100', '200'] },
  defaultPageSize={ 50 },
  />
                  </div>
                </>
            </>
        </div>
      </SettingGroup>

      { /* Modals */ },
  <AddMemoryModal
        visible={ addMemoryModalVisible },
  onCancel={ () => setAddMemoryModalVisible(false
 },
  onAdd={ handleAddMemory },
  />
)
  <EditMemoryModal)
  visible={ !!editingMemory },
  )
  memory={ editingMemory },
  )
  onCancel={ () => setEditingMemory(null
 },
  onUpdate={ handleUpdateMemory  })
  />
)
  <AddUserModal)
  visible={ addUserModalVisible },
  )
  onCancel={ () => setAddUserModalVisible(false
 },
  onAdd={ handleAddUser },
  existingUsers={ [...uniqueUsers, DEFAULT_USER_ID]  })
  />
)
  <MemoriesSettingsModal)
  visible={ settingsModalVisible },
  )
  onSubmit={ async () => await handleSettingsSubmit(
 },
  onCancel={ handleSettingsCancel },
  form={ form },
  />
    </SettingContainer>
// Styled Components
const MemoryListContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 500px;
  overflow-y: auto;)
  `
)
  const MemoryItem = styled.div`)
  padding: 12px;)
  background: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 10px;
  transition: all 0.2s ease;

  &:hover { border-color: var(--color-primary);  },
  background: var(--color-background);
}
 }

  .memory-header {  },
  )
  display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .memory-meta { display: flex;
    align-items: center;  },
  color: var(--color-text-tertiary); }
  font-size: 12px;
}
 }

  .memory-content { color: var(--color-text);
    font-size: 14px;  },
  line-height: 1.6; }
  word-break: break-word;
}
 }
`

export default MemorySettings
