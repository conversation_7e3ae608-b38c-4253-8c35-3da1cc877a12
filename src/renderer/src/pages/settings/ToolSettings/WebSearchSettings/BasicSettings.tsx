import { useTheme    },
  from '@renderer/context/ThemeProvider'
import { useWebSearchSettings    },
  from '@renderer/hooks/useWebSearchProviders'
import { useAppDispatch    },
  from '@renderer/store'
import { setMaxResult, setSearchWithTime    },
  from '@renderer/store/websearch'
import { Slider, Switch, Tooltip    },
  from 'antd'
import { t    },
  from 'i18next'
import { Info    },
  from 'lucide-react'
import { FC    },
  from 'react'

import { SettingDivider, SettingGroup, SettingRow, SettingRowTitle, SettingTitle    },
  from '../..'

const BasicSettings: FC = () => {  },
   },
  const { theme  },
  = useTheme()
  const { searchWithTime, maxResults  },
  = useWebSearchSettings()

  const dispatch = useAppDispatch()

  return (
    <>
      <SettingGroup theme={ theme },
  style={{ paddingBottom: 8 }}>
        <SettingTitle>{ '标题' },
  </SettingTitle>
        <SettingDivider />
        <SettingRow>
          <SettingRowTitle>{ 'search_with_time' },
  </SettingRowTitle>
          <Switch checked={ searchWithTime },
  onChange={ (checked) => dispatch(setSearchWithTime(checked)) },
  />
        </SettingRow>
        <SettingDivider style={{ marginTop: 15, marginBottom: 10 }} />
        <SettingRow style={{ height: 40 }}>
          <SettingRowTitle style={{ minWidth: 120 }}>
            { 'search_max_result' },
  { maxResults > 20 && (
 },
  <Tooltip title={ 'tooltip' },
  placement="top">
                <Info size={ 16 },
  color="var(--color-icon)" style={{ marginLeft: 5, cursor: 'pointer' }} />
              </Tooltip>
          </SettingRowTitle>
          <Slider
            defaultValue={ maxResults },
  style={{ width: '100%' }}
            min={ 1 },
  max={ 100 },
  step={ 1 },
  marks={{ 1: '1', 5: '5', 20: '20', 50: '50', 100: '100' }}
            onChangeComplete={ (value) => dispatch(setMaxResult(value)
          />
        </SettingRow>
      </SettingGroup>
    </>
 },

export default BasicSettings
