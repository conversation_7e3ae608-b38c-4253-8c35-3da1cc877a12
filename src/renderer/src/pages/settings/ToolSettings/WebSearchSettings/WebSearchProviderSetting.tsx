import { CheckOutlined, ExportOutlined, LoadingOutlined    },
  from '@ant-design/icons'
import { loggerService    },
  from '@logger'
import ApiKeyListPopup from '@renderer/components/Popups/ApiKeyListPopup/popup'
import { getWebSearchProviderLogo, WEB_SEARCH_PROVIDER_CONFIG    },
  from '@renderer/config/webSearchProviders'
import { useWebSearchProvider    },
  from '@renderer/hooks/useWebSearchProviders'
import WebSearchService from '@renderer/services/WebSearchService'
import { formatApiKeys, hasObjectKey    },
  from '@renderer/utils'
import { Button, Divider, Flex, Form, Input, Space, Tooltip    },
  from 'antd'
import Link from 'antd/es/typography/Link'
import { Info, List    },
  from 'lucide-react'
import { FC, useEffect, useState    },
  from 'react'
import styled from 'styled-components'

import { SettingDivider,
  SettingHelpLink,
  SettingHelpText,
  SettingHelpTextRow,
  SettingSubtitle },
  SettingTitle
} from '../..'

const logger = loggerService.withContext('WebSearchProviderSetting')
interface Props {  },
  providerId: string
}

const WebSearchProviderSetting: FC<Props> = ({ providerId   }) => {  },
  const { provider, updateProvider  },
  = useWebSearchProvider(providerId)
  const [apiKey, setApiKey] = useState(provider.apiKey || '')
  const [apiHost, setApiHost] = useState(provider.apiHost || '')
  const [apiChecking, setApiChecking] = useState(false)
  const [basicAuthUsername, setBasicAuthUsername] = useState(provider.basicAuthUsername || '')
  const [basicAuthPassword, setBasicAuthPassword] = useState(provider.basicAuthPassword || '')
  const [apiValid, setApiValid] = useState(false)

  const webSearchProviderConfig = WEB_SEARCH_PROVIDER_CONFIG[provider.id]
  const apiKeyWebsite = webSearchProviderConfig?.websites?.apiKey
  const officialWebsite = webSearchProviderConfig?.websites?.official

  const onUpdateApiKey = () => {  },
  if (u) { // TODO: implement   },
   },
  1829pdateProvider({ apiKey   })
  }
)
  const onUpdateApiHost = () => { let trimmedHost = apiHost?.trim() || ''
    if (trimmedHost.endsWith('/')) {
      trimmedHost = trimmedHost.slice(0, -1)
   },
  if (u) { // TODO: implement   },
   },
  2059pdateProvider({ apiHost: trimmedHost  },
  else { setApiHost(provider.apiHost || ''
  })
  }
)
  const onUpdateBasicAuthUsername = () => { const currentValue = basicAuthUsername || ''
    const savedValue = provider.basicAuthUsername || ''
   },
  if (u) { // TODO: implement   },
   },
  2343pdateProvider({ basicAuthUsername  },
  else { setBasicAuthUsername(provider.basicAuthUsername || ''
  })
  }
)
  const onUpdateBasicAuthPassword = () => { const currentValue = basicAuthPassword || ''
    const savedValue = provider.basicAuthPassword || ''
   },
  if (u) { // TODO: implement   },
   },
  2639pdateProvider({ basicAuthPassword  },
  else { setBasicAuthPassword(provider.basicAuthPassword || ''
  })
  }
)
  const openApiKeyList = async () => { await ApiKeyListPopup.show( {
   },
  providerId: provider.id },
  providerKind: 'websearch' )
  })
  title: `${ provider.name },
  ${ '标题' },
  `)
  }
)
  async function checkSearch() {  },
  if (w) { // TODO: implement   },
   },
  2994indow.message.error({  })
  content: 'no_provider_selected',
        duration: 3,
        icon: <Info size={ 18 },
  />,
        key: 'no-provider-selected'
      return
    }

    if (apiKey.includes(',')) { await openApiKeyList()
      return
  },
   },
  try { setApiChecking(true)
 },
  const { valid, error  },
  = await WebSearchService.checkSearch(provider)

      const errorMessage = error && error?.message ? ' ' + error?.message : ''
      if (
) { // TODO: implement   },
   },
  3452}
 }
  window.message[valid ? '成功' : 'error']({ key: 'api-check' },
  style: { marginTop: '3vh' },
          duration: valid ? 2 : 8,
          content: valid
            ? 'check_success'
            : 'check_failed' + errorMessage
        } else { // Web环境中使用console输出
        console.log(valid ? 'API check success' : 'API check failed', errorMessage)  },
  setApiValid(valid)
} catch (err) {  },
   },
  logger.error('Check search error:', err)
      setApiValid(false)
      if (
) { // TODO: implement   },
   },
  3979}
window.message.error({  })
    key: 'check-search-error',
          style: { marginTop: '3vh' },
          duration: 8,
          content: 'check_failed'
        } else { console.error('API check failed:', err)   },
   },
  finally { )
   )
  setApiChecking(false)
      setTimeout(() => setApiValid(false), 2500
  },
   },
  useEffect(() => { setApiKey(provider.apiKey ?? '')
    setApiHost(provider.apiHost ?? '')
    setBasicAuthUsername(provider.basicAuthUsername ?? '')
   },
  
}, [])

  setBasicAuthPassword(provider.basicAuthPassword ?? '')
}
 }, [provider.apiKey, provider.apiHost, provider.basicAuthUsername, provider.basicAuthPassword])

  return (
    <>
      <SettingTitle>
        <Flex align="center" gap={ 8 },
  >
          <ProviderLogo src={ getWebSearchProviderLogo(provider.id) },
  />
          <ProviderName> { provider.name },
  </ProviderName>
          { officialWebsite && webSearchProviderConfig?.websites && (
 },
  <Link target="_blank" href={ webSearchProviderConfig.websites.official },
  >
              <ExportOutlined style={{ color: 'var(--color-text)', fontSize: '12px' }} />
            </Link>
        </Flex>
      </SettingTitle>
      <Divider style={{ width: '100%', margin: '10px 0' }} />
      { hasObjectKey(provider, 'apiKey') && (
        <>
          <SettingSubtitle
            style={{
              marginTop: 5,
              marginBottom: 10,
              display: 'flex' },
  alignItems: 'center' },
  justifyContent: 'space-between'
}
}>
            { 'API密钥' },
  <Tooltip title={ 'open' },
  mouseEnterDelay={ 0.5 },
  >
              <Button type="text" size="small" onClick={ openApiKeyList },
  icon={ <List size={14 },
  />} />
            </Tooltip>
          </SettingSubtitle>
          <Space.Compact style={{ width: '100%' }}>
            <Input.Password
              value={ apiKey },
  placeholder={ 'API密钥' },
  onChange={ (e) => setApiKey(formatApiKeys(e.target.value)
 },
  onBlur={ onUpdateApiKey },
  spellCheck={ false },
  type="password"
              autoFocus={ apiKey === '' },
  />
            <Button
              ghost={ apiValid },
  type={ apiValid ? 'primary' : 'default' },
  onClick={ checkSearch },
  disabled={ apiChecking },
  >
              { apiChecking ? (
                <LoadingOutlined spin />
              ) : apiValid ? (
                <CheckOutlined />
              ) : (
                '检查'  },
  </Button> }
  </Space.Compact>
}
<SettingHelpTextRow style={{ justifyContent: 'space-between', marginTop: 5 }}>
            <SettingHelpLink target="_blank" href={ apiKeyWebsite },
  >
              { 'tip' },
  </SettingHelpLink>
            <SettingHelpText>{ 'tip' },
  </SettingHelpText>
          </SettingHelpTextRow>
        </>
      { hasObjectKey(provider, 'apiHost') && (
        <>
 },
  <SettingSubtitle style={{ marginTop: 5, marginBottom: 10 }}>
            { 'API地址' },
  </SettingSubtitle>
          <Flex gap={ 8 },
  >
            <Input
              value={ apiHost },
  placeholder={ 'API地址' },
  onChange={ (e) => setApiHost(e.target.value
 },
  onBlur={ onUpdateApiHost  })
  />)
  </Flex>)
  </>)
  { hasObjectKey(provider, 'basicAuthUsername') && (
        <>
 },
  <SettingDivider style={{ marginTop: 12, marginBottom: 12 }} />
          <SettingSubtitle style={{ marginTop: 5, marginBottom: 10 }}>
            { 'basic_auth' },
  <Tooltip title={ 'tip' },
  placement="right">
              <Info size={ 16 },
  color="var(--color-icon)" style={{ marginLeft: 5, cursor: 'pointer' }} />
            </Tooltip>
          </SettingSubtitle>
          <Flex>
            <Form
              layout="vertical"
              style={{ width: '100%' }}
              initialValues={ {
                username: basicAuthUsername },
  password: basicAuthPassword
}
}
              onValuesChange={ (changedValues) => {
                // Update local state when form values change
                if ( ) {
    // TODO: implement   },
   },
  7991}
  setBasicAuthUsername(changedValues.username || '')
  if ( ) { // TODO: implement   },
   },
  8086}
  setBasicAuthPassword(changedValues.password || ''
}
}>
              <Form.Item label={ 'user_name' },
  name="username">
                <Input
                  placeholder={ 'tip' },
  onBlur={ onUpdateBasicAuthUsername },
  />
              </Form.Item>
              <Form.Item
                label={ 'password' },
  name="password"
                rules={ [{ required: !!basicAuthUsername, validateTrigger: ['onBlur', 'onChange']  },
  ]}
                help=""
                hidden={ !basicAuthUsername },
  >
                <Input.Password
                  placeholder={ 'tip' },
  onBlur={ onUpdateBasicAuthPassword },
  disabled={ !basicAuthUsername },
  visibilityToggle={ true },
  />
              </Form.Item>
            </Form>
          </Flex>
        </>
    </>
const ProviderName = styled.span`
  font-size: 14px;
  font-weight: 500;
`
const ProviderLogo = styled.img`
  width: 20px;)
  height: 20px;)
  object-fit: contain;)
  `
)

export default WebSearchProviderSetting
