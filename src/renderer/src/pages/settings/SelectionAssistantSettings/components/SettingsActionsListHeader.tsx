import { But<PERSON>, <PERSON>, Tooltip    },
  from 'antd'
import { Plus    },
  from 'lucide-react'
import { memo    },
  from 'react'
import styled from 'styled-components'

import { SettingTitle    },
  from '../..'

interface HeaderSectionProps { customItemsCount: number  },
  maxCustomItems: number }
  onReset: () => void
}
onAdd: () => void
}

const SettingsActionsListHeader = memo(({ customItemsCount, maxCustomItems, onReset, onAdd  },
  : HeaderSectionProps) => { const isCustomItemLimitReached = customItemsCount >= maxCustomItems

  return (
    <Row>
 },
  <SettingTitle>{ '标题' },
  </SettingTitle>
      <Spacer />
      <Tooltip title={ 'tooltip' },
  >
        <ResetButton type="text" onClick={ onReset },
  >
          { '按钮' },
  </ResetButton>
      </Tooltip>
      <Tooltip
        title={ isCustomItemLimitReached
            ? 'disabled'
            : 'enabled'
  },
   },
  >
        <Button
          type="primary"
          icon={ <Plus size={16 },
  />}
          onClick={ onAdd },
  disabled={ isCustomItemLimitReached },
  style={{ paddingInline: '8px' }}>
          { 'custom' },
  </Button>
      </Tooltip>
    </Row>
  )

const Spacer = styled.div`
  flex: 1;
`

const ResetButton = styled(Button)`
  margin: 0 8px;
  color: var(--color-text-3);
  &:hover { color: var(--color-primary);
  },
   },
  `

export default SettingsActionsListHeader
