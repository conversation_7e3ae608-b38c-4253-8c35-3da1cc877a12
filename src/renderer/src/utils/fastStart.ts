/**
 * 快速启动优化工具
 * 用于优化应用启动性能
 */

// 启动性能监控

export class StartupPerformance {
  private static instance: StartupPerformance
  private startTime: number
  private milestones: Map<string, number> = new Map()

  constructor() {
    this.startTime = performance.now()
  }

  static getInstance(): StartupPerformance {
    if (!StartupPerformance.instance) {
      StartupPerformance.instance = new StartupPerformance()
    }
    return StartupPerformance.instance
  }

  mark(name: string): void {
    const time = performance.now()
    this.milestones.set(name, time)
    console.log(`🚀 [${name}] ${(time - this.startTime).toFixed(2)} ms`)
  }

  getReport(): Record<string, number> {
    const report: Record<string, number> = {}
    this.milestones.forEach((time, name) => {
      report[name] = time - this.startTime
    })
    return report
  }

  getTotalTime(): number {
    return performance.now() - this.startTime
  }
}

// 资源预加载器

export class ResourcePreloader {
  private static preloadedResources = new Set<string>()

  static preloadImage(src: string): Promise<void> {
    if (this.preloadedResources.has(src)) {
      return Promise.resolve()
    }
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        this.preloadedResources.add(src)
        resolve()
      }
      img.onerror = reject
      img.src = src
    })
  }

  static preloadScript(src: string): Promise<void> {
    if (this.preloadedResources.has(src)) {
      return Promise.resolve()
    }
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.onload = () => {
        this.preloadedResources.add(src)
        resolve()
      }
      script.onerror = reject
      script.src = src
      document.head.appendChild(script)
    })
  }

  static preloadStyle(href: string): Promise<void> {
    if (this.preloadedResources.has(href)) {
      return Promise.resolve()
    }
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.onload = () => {
        this.preloadedResources.add(href)
        resolve()
      }
      link.onerror = reject
      link.href = href
      document.head.appendChild(link)
    })
  }
}

// 延迟加载管理器

export class LazyLoadManager {
  private static tasks: Array<() => Promise<void>> = []
  private static isProcessing = false

  static addTask(task: () => Promise<void>): void {
    this.tasks.push(task)
    this.processTasks()
  }

  private static async processTasks(): Promise<void> {
    if (this.isProcessing) {
      return
    }

    this.isProcessing = true

    // 使用 requestIdleCallback 在空闲时执行任务
    const processNextTask = () => {
      if (this.tasks.length === 0) {
        this.isProcessing = false
        return
      }

      const task = this.tasks.shift()
      if (task) {
        task().catch(console.error).finally(() => {
          // 在下一个空闲时间继续处理
          if (window.requestIdleCallback) {
            requestIdleCallback(processNextTask)
          } else {
            setTimeout(processNextTask, 0)
          }
        })
      }
    }

    if (window.requestIdleCallback) {
      requestIdleCallback(processNextTask)
    } else {
      setTimeout(processNextTask, 0)
    }
  }
}

// 启动优化配置

export const startupConfig = {
  // 是否启用快速启动模式
  enableFastStart: true,
  
  // 是否跳过非关键初始化
  skipNonCriticalInit: true,
  
  // 延迟加载时间（毫秒）
  lazyLoadDelay: 100,
  
  // 是否启用性能监控
  enablePerformanceMonitoring: process.env.NODE_ENV === 'development',
  
  // 预加载资源列表
  preloadResources: [
    '/src/assets/images/logo.svg',
    '/src/assets/styles/index.scss'
  ]
}

// 快速启动初始化

export async function initFastStart(): Promise<void> {
  const perf = StartupPerformance.getInstance()
  perf.mark('fast-start-init')

  // 预加载关键资源
  if (startupConfig.enableFastStart) {
    const preloadPromises = startupConfig.preloadResources.map(resource => {
      if (resource.endsWith('.svg') || resource.endsWith('.png') || resource.endsWith('.jpg')) {
        return ResourcePreloader.preloadImage(resource)
      } else if (resource.endsWith('.js')) {
        return ResourcePreloader.preloadScript(resource)
      } else if (resource.endsWith('.css') || resource.endsWith('.scss')) {
        return ResourcePreloader.preloadStyle(resource)
      }
      return Promise.resolve()
    })

    await Promise.allSettled(preloadPromises)
    perf.mark('resources-preloaded')
  }

  // 优化滚动性能
  document.documentElement.style.scrollBehavior = 'smooth'

  // 设置视口优化
  const viewport = document.querySelector('meta[name="viewport"]')
  if (viewport) {
    viewport.setAttribute('content', 'width=device-width, initial-scale=1, viewport-fit=cover')
  }
  
  perf.mark('fast-start-complete')
}

// 导出性能监控实例
export const perf = StartupPerformance.getInstance()
