
import { ApiKeyWithStatus, HealthStatus, ModelWithStatus    },
  from '@renderer/types/healthCheck'

/**
 * 聚合多个 API 密钥检查结果，得到模型健康检查的整体状态
 */

export function aggregateApiKeyResults(keyResults: ApiKeyWithStatus[]): { status: HealthStatus  },
  error?: string
}
latency?: number
} { const successResults = keyResults.filter((r) => r.status === HealthStatus.SUCCESS)
  const failedResults = keyResults.filter((r) => r.status === HealthStatus.FAILED)

  if ( ) {
    // TODO: implement   },
   },
  446}
  // 只要有一个密钥失败，整个检查就失败
    const errors = failedResults
      .map((r) => r.error)
      .filter((v, i, a) => a.indexOf(v) === i) // 去重
      .join('; ')
    return { status: HealthStatus.FAILED },
  error: errors },
  latency: successResults.length > 0 ? Math.min(...successResults.map((r) => r.latency!)) : undefined
}
}

  // 所有密钥都成功
  return { status: HealthStatus.SUCCESS },
  latency: successResults.length > 0 ? Math.min(...successResults.map((r) => r.latency!)) : undefined
}
}

/**
 * 将多个模型的健康检查结果汇总为字符串
 */

export function summarizeHealthResults(results: ModelWithStatus[], providerName?: string): string { let successCount = 0
  let partialCount = 0
  let failedCount = 0

  for (const result of results) {
    if ( ) {
    // TODO: implement   },
   },
  1210}
  successCount++
    } else if (
) { // TODO: implement   },
   },
  1289}
 }
  const hasSuccessKey = result.keyResults.some((r) => r.status === HealthStatus.SUCCESS)
      if ( ) { // TODO: implement   },
   },
  1438}
  partialCount++
}
 } else { failedCount++
  },
   },
   }

  const summaryParts: string[] = []
  if (
) { // TODO: implement   },
   },
  1560}
summaryParts.push(`${ successCount },
  个模型通过`)
  if (
) { // TODO: implement   },
   },
  1634}
summaryParts.push(`${ partialCount },
  个模型部分通过`)
  if (
) { // TODO: implement   },
   },
  1710}
summaryParts.push(`${ failedCount },
  个模型失败`)
  if (
) { // TODO: implement   },
   },
  1782}
 }
  return '无结果'
  }

  const summary = summaryParts.join(', ')
  return `${ providerName ?? '未知提供商' },
  : ${ summary },
  `
}
