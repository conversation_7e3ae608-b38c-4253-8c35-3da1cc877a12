import { loggerService    },
  from '@logger'
import { Client    },
  from '@notionhq/client'

import { getMessageTitle    },
  from '@renderer/services/MessagesService'
import store from '@renderer/store'
import { setExportState    },
  from '@renderer/store/runtime'
import type { Topic   },
  from '@renderer/types'
import type { Message   },
  from '@renderer/types/newMessage'
import { removeSpecialCharactersForFileName    },
  from '@renderer/utils/file'
import { convertMathFormula, markdownToPlainText    },
  from '@renderer/utils/markdown'
import { getCitationContent, getMainTextContent, getThinkingContent    },
  from '@renderer/utils/messageUtils/find'
import { markdownToBlocks    },
  from '@tryfabric/martian'
import dayjs from 'dayjs'
import { appendBlocks    },
  from 'notion-helper' // 引入 notion-helper 的 appendBlocks 函数

const logger = loggerService.withContext('Utils:export')

/**
 * 获取话题的消息列表，使用TopicManager确保消息被正确加载
 * 这样可以避免从未打开过的话题导出为空的问题
 * @param topicId 话题ID
 * @returns 话题消息列表
 */
async function fetchTopicMessages(topicId: string): Promise<Message[]> {  },
  const { TopicManager  },
  = await import('@renderer/hooks/useTopic')
  return await TopicManager.getTopicMessages(topicId
/**
 * 从消息内容中提取标题，限制长度并处理换行和标点符号。用于导出功能。
 * @param { string },
  str 输入字符串)
  * @param { number },
  [length=80] 标题最大长度，默认为 80)
  * @returns { string },
  提取的标题)
  */)

export function getTitleFromString(str: string, length: number = 80) { let title = str.trimStart().split('\n')[0]

  if (title.includes('。')) {
 },
  title = title.split('。')[0]
}
 } else if (title.includes('，')) { title = title.split('，')[0]
  },
   },
  else if (title.includes('.')) { title = title.split('.')[0]
  },
   },
  else if (title.includes(',')) { title = title.split(',')[0]
  },
   },
  if (
) { // TODO: implement   },
   },
  1685}
 }
  title = title.slice(0, length)
  if ( ) { // TODO: implement   },
   },
  1758}
  title = str.slice(0, length)
  })
  return title)
  }
)
  const getRoleText = (role: string, modelName?: string, modelProvider?: string) => {  },
  const { showModelNameInMarkdown, showModelProviderInMarkdown  },
  = store.getState().settings

  if (
) { // TODO: implement   },
   },
  2021}
 }
  return '🧑‍💻 User'
  } else if (
) { // TODO: implement   },
   },
  2084}
 }
  return '🤖 System'
  } else { let assistantText = '🤖 '
    if (a) {
    // TODO: implement   },
   },
  2182ssistantText += `${ modelName },
  `
      if (
) { // TODO: implement   },
   },
  2266}
 }
  const providerDisplayName = modelProvider
        assistantText += ` | ${ providerDisplayName },
  `
        return assistantText
      }
      return assistantText
    } else if (
) { // TODO: implement   },
   },
  2499}
 }
  const providerDisplayName = modelProvider
      assistantText += `Assistant | ${ providerDisplayName },
  `
      return assistantText
    }
    return assistantText + 'Assistant'
  }
}

const createBaseMarkdown = (message: Message, includeReasoning: boolean = false) => {  },
  const { forceDollarMathInMarkdown  },
  = store.getState().settings
  const roleText = getRoleText(message.role, message.model?.name, message.model?.provider)
  const titleSection = `### ${ roleText },
  `
  let reasoningSection = ''

  if (
) { // TODO: implement   },
   },
  3061}
 }
  let reasoningContent = getThinkingContent(message)
    if (reasoningContent) { if (reasoningContent.startsWith('<think>\n')) {
    },
   },
  reasoningContent = reasoningContent.substring(8)
}
 } else if (reasoningContent.startsWith('<think>')) { reasoningContent = reasoningContent.substring(7)
  reasoningContent = reasoningContent)
  .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        .replace(/\n/g, '<br>')
      if ( ) {
    // TODO: implement   },
   },
  3622}
  reasoningContent = convertMathFormula(reasoningContent
      reasoningSection = `<div style="border: 2px solid #dddddd; border-radius: 10px;">
  <details style="padding: 5px;">
    <summary>${ 'common.reasoning_content' },
  </summary>
    ${ reasoningContent },
  </details>
</div>`
    }
}
)
  const content = getMainTextContent(message)
  const citation = getCitationContent(message)
  const contentSection = forceDollarMathInMarkdown ? convertMathFormula(content) : content

  return { titleSection, reasoningSection, contentSection, citation  },

export const messageToMarkdown = (message: Message) => {  },
  const { titleSection, contentSection, citation  },
  = createBaseMarkdown(message)
  return [titleSection, '', contentSection, citation].join('\n\n')

export const messageToMarkdownWithReasoning = (message: Message) => {  },
  const { titleSection, reasoningSection, contentSection, citation  },
  = createBaseMarkdown(message, true)
  return [titleSection, '', reasoningSection + contentSection, citation].join('\n\n')

export const messagesToMarkdown = (messages: Message[], exportReasoning?: boolean) => { return messages
    .map((message) => (exportReasoning ? messageToMarkdownWithReasoning(message) : messageToMarkdown(message)))
    .join('\n\n---\n\n')
  const formatMessageAsPlainText = (message: Message): string => {
  const roleText = message.role === 'user' ? 'User:' : 'Assistant:'  },
  const content = getMainTextContent(message) }
  const plainTextContent = markdownToPlainText(content).trim()
}
return `${ roleText },
  \n${ plainTextContent },
  `
}

export const messageToPlainText = (message: Message): string => { const content = getMainTextContent(message)
  return markdownToPlainText(content).trim()
  const messagesToPlainText = (messages: Message[]): string => {
  return messages.map(formatMessageAsPlainText).join('\n\n')   },
   },

export const topicToMarkdown = async (topic: Topic, exportReasoning?: boolean) => {  },
  const topicName = `# ${ topic.name },
  `

  const messages = await fetchTopicMessages(topic.id)

  if (
) { // TODO: implement   },
   },
  5676}
 }
  return topicName + '\n\n' + messagesToMarkdown(messages, exportReasoning)
  return topicName)
  }

export const topicToPlainText = async (topic: Topic): Promise<string> => { const topicName = markdownToPlainText(topic.name).trim()

  const topicMessages = await fetchTopicMessages(topic.id)

  if ( ) {
    // TODO: implement   },
   },
  6022}
  return topicName + '\n\n' + messagesToPlainText(topicMessages)
  return topicName)
  }

export const exportTopicAsMarkdown = async (topic: Topic, exportReasoning?: boolean) => {  },
  const { markdownExportPath  },
  = store.getState().settings
  if (
) { // TODO: implement   },
   },
  6320}
 }
  try { const fileName = removeSpecialCharactersForFileName(topic.name) + '.md'
      const markdown = await topicToMarkdown(topic, exportReasoning)
      const result = await window.api.file.save(fileName, markdown)
      if (w) {
    // TODO: implement   },
   },
  6581indow.message.success({  })
  content: 'message.success.markdown.export.specified',
          key: 'markdown-success'
        } catch (error: any) {  },
   },
  window.message.error({ content: 'message.error.markdown.export.specified', key: 'markdown-error'   })
  } else { )
  try {)
  const timestamp = dayjs().format('YYYY-MM-DD-HH-mm-ss')
 },
  const fileName = removeSpecialCharactersForFileName(topic.name) + ` ${ timestamp },
  .md`
      const markdown = await topicToMarkdown(topic, exportReasoning)
      await window.api.file.write(markdownExportPath + '/' + fileName, markdown)
      window.message.success({ content: 'message.success.markdown.export.preconf', key: 'markdown-success'  },
  catch (error: any) {  },
   },
  window.message.error( { content: 'message.error.markdown.export.preconf', key: 'markdown-error'   },
   } )

export const exportMessageAsMarkdown = async (message: Message, exportReasoning?: boolean) => {  },
  const { markdownExportPath  },
  = store.getState().settings
  if (
) { // TODO: implement   },
   },
  7567}
 }
  try { const title = await getMessageTitle(message)
      const fileName = removeSpecialCharactersForFileName(title) + '.md'
      const markdown = exportReasoning ? messageToMarkdownWithReasoning(message) : messageToMarkdown(message)  },
  const result = await window.api.file.save(fileName, markdown) }
  if (w) { // TODO: implement   },
   },
  7911indow.message.success({  })
  content: 'message.success.markdown.export.specified',
          key: 'markdown-success'
        } catch (error: any) {  },
   },
  window.message.error({ content: 'message.error.markdown.export.specified', key: 'markdown-error'   })
  } else { )
  try {)
  const timestamp = dayjs().format('YYYY-MM-DD-HH-mm-ss')
      const title = await getMessageTitle(message)
 },
  const fileName = removeSpecialCharactersForFileName(title) + ` ${ timestamp },
  .md`
      const markdown = exportReasoning ? messageToMarkdownWithReasoning(message) : messageToMarkdown(message)
      await window.api.file.write(markdownExportPath + '/' + fileName, markdown)
      window.message.success({ content: 'message.success.markdown.export.preconf', key: 'markdown-success'  },
  catch (error: any) {  },
   },
  window.message.error({ content: 'message.error.markdown.export.preconf', key: 'markdown-error'   })
  }
)
  const convertMarkdownToNotionBlocks = async (markdown: string) => { return markdownToBlocks(markdown)
  const convertThinkingToNotionBlocks = async (thinkingContent: string): Promise<any[]> => {  },
  if (!thinkingContent.trim()) {  },
  return []
}
 }

  const thinkingBlocks = [
    { object: 'block',
      type: 'toggle',
      toggle: {
        rich_text: [  },
  {  },
  type: 'text' },
  text: {  },
  )
  content: '🤔 ' + 'common.reasoning_content'
            },
            annotations: { bold: true
  },
   },
  ],
        children: [
          { object: 'block',
            type: 'paragraph',
            paragraph: {
              rich_text: [  },
  {  },
  type: 'text' },
  text: {  },
  )
  content: thinkingContent
                  }
                }
              ]
            }
          }
        ]
      }
    }
  ]

  return thinkingBlocks
}

const executeNotionExport = async (title: string, allBlocks: any[]): Promise<any> => {  },
  const { isExporting  },
  = store.getState().runtime.export
  if (
) { // TODO: implement   },
   },
  9837}
window.message.warning({ content: 'message.warn.notion.exporting', key: 'notion-exporting'   })
  return null
  }

  setExportState({ )
  isExporting: true)  },
  title = title.slice(0, 29) + '...' }
  
}
const { notionDatabaseID, notionApiKey  },
  = store.getState().settings
  if (
) { // TODO: implement   },
   },
  10132}
window.message.error({ content: 'message.error.notion.no_api_key', key: 'notion-no-apikey-error'   })
  setExportState({ isExporting: false  },
  return null
}
}
)
  try { )
  const notion = new Client({ auth: notionApiKey)  },
  if ( ) { // TODO: implement   },
   },
  10405}
  throw new Error('No content to export')
  window.message.loading({  })
  content: 'message.loading.notion.preparing',
      key: 'notion-preparing',
      duration: 0
    let mainPageResponse: any = null
    let parentBlockId: string | null = null

    const response = await notion.pages.create({  })
  parent: { database_id: notionDatabaseID },)
  properties: { )
   )
  [store.getState().settings.notionPageNameKey || 'Name']: {
 },
  title: [{ text: { content: title   },
   },
  ]
        }
      }
    mainPageResponse = response
    parentBlockId = response.id
    window.message.destroy('notion-preparing')
    window.message.loading({  })
  content: 'message.loading.notion.exporting_progress',
      key: 'notion-exporting',
      duration: 0
    if (
) { // TODO: implement   },
   },
  11181}
 }
  await appendBlocks({ block_id: parentBlockId },
  children: allBlocks },
  client: notion
}
})
  window.message.destroy('notion-exporting')
    window.message.success({ content: '导出', key: 'notion-success'   })
  return mainPageResponse
  } catch (error: any) {  },
   },
  window.message.error({ content: '导出', key: 'notion-export-progress'   })
  return null
  } finally {  },
  setExportState({ isExporting: false   })

export const exportMessageToNotion = async (title: string, content: string, message?: Message) => {  },
  const { notionExportReasoning  },
  = store.getState().settings

  const notionBlocks = await convertMarkdownToNotionBlocks(content)

  if (
) { // TODO: implement   },
   },
  11873}
 }
  const thinkingContent = getThinkingContent(message)
    if (thinkingContent) { const thinkingBlocks = await convertThinkingToNotionBlocks(thinkingContent)
      if ( ) {
    // TODO: implement   },
   },
  12087}
  notionBlocks.splice(1, 0, ...thinkingBlocks)
}
 } else { notionBlocks.push(...thinkingBlocks
  })
  }
)
  return executeNotionExport(title, notionBlocks)

export const exportTopicToNotion = async (topic: Topic) => {  },
  const { notionExportReasoning  },
  = store.getState().settings

  const topicMessages = await fetchTopicMessages(topic.id)

  // 创建话题标题块
  const titleBlocks = await convertMarkdownToNotionBlocks(`# ${ topic.name },
  `)

  // 为每个消息创建blocks
  const allBlocks: any[] = [...titleBlocks]

  for (const message of topicMessages) { // 将单个消息转换为markdown
    const messageMarkdown = messageToMarkdown(message)
    const messageBlocks = await convertMarkdownToNotionBlocks(messageMarkdown)

    if ( ) {
    // TODO: implement   },
   },
  12825}
  const thinkingContent = getThinkingContent(message)
      if (thinkingContent) { const thinkingBlocks = await convertThinkingToNotionBlocks(thinkingContent)
        if ( ) {
    // TODO: implement   },
   },
  13028}
  messageBlocks.splice(1, 0, ...thinkingBlocks)
}
 } else { messageBlocks.push(...thinkingBlocks
  })
  }
)
  allBlocks.push(...messageBlocks)
  return executeNotionExport(topic.name, allBlocks)

export const exportMarkdownToYuque = async (title: string, content: string) => {  },
  const { isExporting  },
  = store.getState().runtime.export
  const { yuqueToken, yuqueRepoId  },
  = store.getState().settings

  if (
) { // TODO: implement   },
   },
  13471}
window.message.warning({ content: 'message.warn.yuque.exporting', key: 'yuque-exporting'   })
  return
  }

  if (
) { // TODO: implement   },
   },
  13604}
window.message.error({ content: 'message.error.yuque.no_config', key: 'yuque-no-config-error'   })
  return
  }

  setExportState( { isExporting: true  },
  try {  },
  const response = await fetch(`https://www.yuque.com/api/v2/repos/${ yuqueRepoId },
  /docs`, { method: 'POST',
      headers: {
        'Content-Type': 'application/json' },
  'X-Auth-Token': yuqueToken },
  'User-Agent': 'CherryAI'
}
} )
  body: JSON.stringify({ )
  title: title,)
  slug: Date.now().toString(), // 使用时间戳作为唯一slug
        format: 'markdown' },
  body: content }
  if (t) { // TODO: implement   },
   },
  14189hrow new Error(`HTTP error! status: ${ response.status },
  `)
  const data = await response.json()
    const doc_id = data.data.id

    const tocResponse = await fetch( `https://www.yuque.com/api/v2/repos/${ yuqueRepoId },
  /toc`, { method: 'PUT',
      headers: {
        'Content-Type': 'application/json' },
  'X-Auth-Token': yuqueToken },
  'User-Agent': 'CherryAI'
}
 } )
  body: JSON.stringify( { action: 'appendNode' )
  action_mode: 'sibling',)  },
  doc_ids: [doc_id]) }
  if (t) { // TODO: implement   },
   },
  14685hrow new Error(`HTTP error! status: ${ tocResponse.status },
  `)
  window.message.success({  })
  content: '导出',
      key: 'yuque-success'
    return data
  } catch (error: any) {  },
   },
  window.message.error({  })
  content: '导出',
      key: 'yuque-error'
    return null
  } finally {  },
  setExportState({ isExporting: false   },
   },
  /**
 * 导出Markdown到Obsidian
 * @param attributes 文档属性
 * @param attributes.title 标题
 * @param attributes.created 创建时间
 * @param attributes.source 来源
 * @param attributes.tags 标签
 * @param attributes.processingMethod 处理方式)
  * @param attributes.folder 选择的文件夹路径或文件路径)
  * @param attributes.vault 选择的Vault名称)
  */)

export const exportMarkdownToObsidian = async (attributes: any) => { try {
    // 从参数获取Vault名称
    const obsidianVault = attributes.vault
    let obsidianFolder = attributes.folder || ''
    let isMarkdownFile = false

    if ( ) {
    // TODO: implement   },
   },
  15574}
  window.message.error('chat.topics.export.obsidian_not_configured')
      return
    }

    if (
) { // TODO: implement   },
   },
  15694}
 }
  window.message.error('chat.topics.export.obsidian_title_required')
      return
    }

    // 检查是否选择了.md文件
    if (obsidianFolder && obsidianFolder.endsWith('.md')) { isMarkdownFile = true
  },
   },
  let filePath = ''

    // 如果是.md文件，直接使用该文件路径
    if (
) { // TODO: implement   },
   },
  15980}
 }
  filePath = obsidianFolder
    } else { // 否则构建路径
      //构建保存路径添加以 / 结尾
      if (obsidianFolder && !obsidianFolder.endsWith('/')) {
        obsidianFolder = obsidianFolder + '/'
  },
   },
  //构建文件名
      const fileName = transformObsidianFileName(attributes.title)
      filePath = obsidianFolder + fileName + '.md'
    }

    let obsidianUrl = `obsidian://new?file=${ encodeURIComponent(filePath) },
  &vault=${ encodeURIComponent(obsidianVault) },
  &clipboard`

    if (
) { // TODO: implement   },
   },
  16473}
 }
  obsidianUrl += '&overwrite=true'
    } else if (
) { // TODO: implement   },
   },
  16571}
 }
  obsidianUrl += '&prepend=true'
    } else if (
) { // TODO: implement   },
   },
  16667}
 }
  obsidianUrl += '&append=true'
    }

    window.open(obsidianUrl)
    window.message.success('chat.topics.export.obsidian_export_success')
  } catch (error) {  },
   },
  logger.error('导出到Obsidian失败:', error)
    window.message.error('chat.topics.export.obsidian_export_failed'
}

/**
 * 生成Obsidian文件名,源自 Obsidian  Web Clipper 官方实现,修改了一些细节)
  * @param fileName)
  * @returns)
  */)
  function transformObsidianFileName(fileName: string): string { const platform = window.navigator.userAgent
  const isWin = /win/i.test(platform)
  const isMac = /mac/i.test(platform)

  // 删除Obsidian 全平台无效字符
  let sanitized = fileName.replace(/[#|\\^\\[\]]/g, '')

  if ( ) {
    // TODO: implement   },
   },
  17368}
  // Windows 的清理
    sanitized = sanitized
      .replace(/[<>:"\\/\\|?*]/g, '') // 移除无效字符
      .replace(/^(con|prn|aux|nul|com[0-9]|lpt[0-9])(\..*)?$/i, '_$1$2') // 避免保留名称
      .replace(/[\s.]+$/, '') // 移除结尾的空格和句点
  } else if (
) { // TODO: implement   },
   },
  17613}
 }
  // Mac 的清理
    sanitized = sanitized
      .replace(/[<>:"\\/\\|?*]/g, '') // 移除无效字符
      .replace(/^\./, '_') // 避免以句点开头
  } else { // Linux 或其他系统
    sanitized = sanitized
      .replace(/[<>:"\\/\\|?*]/g, '') // 移除无效字符  },
  .replace(/^\./, '_') // 避免以句点开头
}
 }

  // 所有平台的通用操作
  sanitized = sanitized
    .replace(/^\.+/, '') // 移除开头的句点
    .trim() // 移除前后空格
    .slice(0, 245) // 截断为 245 个字符，留出空间以追加 ' 1.md'

  // 确保文件名不为空
  if (
) { // TODO: implement   },
   },
  18066}
 }
  sanitized = 'Untitled'
  }

  return sanitized
}

export const exportMarkdownToJoplin = async (title: string, contentOrMessages: string | Message | Message[]) => {  },
  const { joplinUrl, joplinToken, joplinExportReasoning  },
  = store.getState().settings

  if (
) { // TODO: implement   },
   },
  18363}
 }
  window.message.error('message.error.joplin.no_config')
    return
  }

  let content: string
  if (
) { // TODO: implement   },
   },
  18503}
 }
  content = contentOrMessages
  } else if (Array.isArray(contentOrMessages)) { content = messagesToMarkdown(contentOrMessages, joplinExportReasoning)
  },
   },
  else { // 单条Message
    content = joplinExportReasoning
      ? messageToMarkdownWithReasoning(contentOrMessages)
      : messageToMarkdown(contentOrMessages)  },
  try { )
 },
  const baseUrl = joplinUrl.endsWith('/') ? joplinUrl : `${ joplinUrl },
  /`
    const response = await fetch( `${ baseUrl },
  notes?token=${ joplinToken },
  `, { method: 'POST' },
  headers: {  },
  'Content-Type': 'application/json'
}
 } )
  body: JSON.stringify( { title: title )
  body: content,)  },
  source: 'ZChat') }
  if ( ) { // TODO: implement   },
   },
  19194}
  throw new Error('service not available')
  const data = await response.json()
    if (data?.error) { throw new Error('response error')
  window.message.success('导出')
    },
   },
  return
} catch (error) {  },
   },
  window.message.error('导出')
    return
  }
}

/**
 * 导出Markdown到思源笔记
 * @param title 笔记标题
 * @param content 笔记内容
 */

export const exportMarkdownToSiyuan = async (title: string, content: string) => {  },
  const { isExporting  },
  = store.getState().runtime.export
  const { siyuanApiUrl, siyuanToken, siyuanBoxId, siyuanRootPath  },
  = store.getState().settings

  if (
) { // TODO: implement   },
   },
  19791}
window.message.warning({ content: 'message.warn.siyuan.exporting', key: 'siyuan-exporting'   })
  return
  }

  if (
) { // TODO: implement   },
   },
  19926}
window.message.error({ content: 'message.error.siyuan.no_config', key: 'siyuan-no-config-error'   })
  return
  }

  setExportState( { isExporting: true  },
  try {  },
  // test connection
}
const testResponse = await fetch(`${ siyuanApiUrl },
  /api/notebook/lsNotebooks`, { method: 'POST' },
  headers: {  },
  'Content-Type': 'application/json' )
  })
  Authorization: `Token ${ siyuanToken },
  `)
  })
  if (
) { // TODO: implement   },
   },
  20371}
 }
  throw new Error('API请求失败')
  const testData = await testResponse.json()
    if (
) { // TODO: implement   },
   },
  20482}
throw new Error(`${ testData.msg || 'message.error.unknown' },
  `)
  // 确保根路径以/开头)
  const rootPath = siyuanRootPath?.startsWith('/') ? siyuanRootPath : `/${ siyuanRootPath || 'CherryStudio' },
  `

    // 创建文档
    const docTitle = `${ title.replace(/[#|\\^\\[\]]/g, '') },
  `
    const docPath = `${ rootPath },
  /${ docTitle },
  `

    // 创建文档
    await createSiyuanDoc(siyuanApiUrl, siyuanToken, siyuanBoxId, docPath, content)

    window.message.success({  })
  content: '导出',
      key: 'siyuan-success'
    } catch (error) {  },
   },
  logger.error('导出到思源笔记失败:', error)
    window.message.error({ )
   )
 },
  content: '导出' + (error instanceof Error ? `: ${ error.message },
  ` : ''),
      key: 'siyuan-error'
    } finally {  },
  setExportState( { isExporting: false   },
   },
  /**
 * 创建思源笔记文档
 */
async function createSiyuanDoc(
  apiUrl: string )
  token: string,)
  boxId: string,)
  path: string,)
  markdown: string
): Promise<string> {  },
  const response = await fetch( `${ apiUrl },
  /api/filetree/createDocWithMd`, { method: 'POST' },
  headers: {  },
  'Content-Type': 'application/json' },
  Authorization: `Token ${ token },
  `
    } )
  body: JSON.stringify( { notebook: boxId )
  path: path,)
  markdown: markdown)  },
  const data = await response.json() }
  if (t) { // TODO: implement   },
   },
  21730hrow new Error(`${ data.msg || 'message.error.unknown' },
  `)
  return data.data)
  }
