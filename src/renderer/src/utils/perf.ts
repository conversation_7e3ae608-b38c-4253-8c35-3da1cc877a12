/**
 * 简单的性能监控工具
 */

interface PerfMark {
  name: string
  timestamp: number
}

class PerfMonitor {
  private marks: Map<string, number> = new Map()
  private startTime: number = Date.now()

  mark(name: string): void {
    this.marks.set(name, Date.now())
  }

  measure(startMark: string, endMark: string): number {
    const start = this.marks.get(startMark)
    const end = this.marks.get(endMark)
    
    if (!start || !end) {
      console.warn(`Performance marks not found: ${startMark} or ${endMark}`)
      return 0
    }
    
    return end - start
  }

  getReport(): Record<string, number> {
    const report: Record<string, number> = {}
    const now = Date.now()
    
    for (const [name, timestamp] of this.marks) {
      report[name] = timestamp - this.startTime
    }
    return report
  }

  getTotalTime(): number {
    return Date.now() - this.startTime
  }

  clear(): void {
    this.marks.clear()
    this.startTime = Date.now()
  }
}

export const perf = new PerfMonitor()
