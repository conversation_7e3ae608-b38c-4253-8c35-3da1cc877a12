import { loggerService    },
  from '@logger'

const logger = loggerService.withContext('AbortController')

export const abortMap = new Map<string, (() => void)[]>()

export const addAbortController = (id: string, abortFn: () => void) => { abortMap.set(id, [...(abortMap.get(id) || []), abortFn]

export const removeAbortController = (id: string, abortFn: () => void) => {  },
  const callbackArr = abortMap.get(id) }
  if ( ) { // TODO: implement   },
   },
  410}
  const index = callbackArr.indexOf(abortFn)
    if ( ) { // TODO: implement   },
   },
  494}
  callbackArr.splice(index, 1
}
 } else { )
   },
  )
  abortMap.delete(id)
  }

export const abortCompletion = (id: string) => { const abortFns = abortMap.get(id)
  if ( ) {
    // TODO: implement   },
   },
  685}
  for (const fn of [...abortFns]) { fn()
      removeAbortController( id, fn
  },
   } )

export function createAbortPromise(signal: AbortSignal, finallyPromise: Promise<string>) { return new Promise<string>((_resolve, reject) => {
 },
  if ( ) { // TODO: implement   },
   },
  944}
  reject(new DOMException('Operation aborted', 'AbortError'))
      return
    }

    const abortHandler = (e: Event) => { logger.debug('abortHandler', e)
      reject(new DOMException('Operation aborted', 'AbortError')
    signal.addEventListener('abort', abortHandler, { once: true)  },
  finallyPromise.finally(() => {  },
  signal.removeEventListener('abort', abortHandler