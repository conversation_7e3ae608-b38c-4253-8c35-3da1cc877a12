<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#gradient1)" opacity="0.1"/>
  
  <!-- Main chat bubble -->
  <path d="M32 40 C32 32, 40 24, 48 24 L80 24 C88 24, 96 32, 96 40 L96 64 C96 72, 88 80, 80 80 L56 80 L40 92 L40 80 C32 80, 32 72, 32 64 Z" fill="url(#gradient1)"/>
  
  <!-- AI brain/neural network dots -->
  <circle cx="52" cy="48" r="3" fill="white" opacity="0.9"/>
  <circle cx="64" cy="48" r="3" fill="white" opacity="0.9"/>
  <circle cx="76" cy="48" r="3" fill="white" opacity="0.9"/>
  
  <circle cx="58" cy="60" r="2.5" fill="white" opacity="0.7"/>
  <circle cx="70" cy="60" r="2.5" fill="white" opacity="0.7"/>
  
  <!-- Neural connections -->
  <line x1="52" y1="48" x2="58" y2="60" stroke="white" stroke-width="1" opacity="0.5"/>
  <line x1="64" y1="48" x2="58" y2="60" stroke="white" stroke-width="1" opacity="0.5"/>
  <line x1="64" y1="48" x2="70" y2="60" stroke="white" stroke-width="1" opacity="0.5"/>
  <line x1="76" y1="48" x2="70" y2="60" stroke="white" stroke-width="1" opacity="0.5"/>
  
  <!-- Secondary chat bubble -->
  <path d="M72 88 C72 84, 76 80, 80 80 L100 80 C104 80, 108 84, 108 88 L108 100 C108 104, 104 108, 100 108 L88 108 L80 116 L80 108 C76 108, 72 104, 72 100 Z" fill="url(#gradient2)" opacity="0.8"/>
  
  <!-- AI indicator dots in second bubble -->
  <circle cx="88" cy="94" r="1.5" fill="white"/>
  <circle cx="94" cy="94" r="1.5" fill="white"/>
  <circle cx="100" cy="94" r="1.5" fill="white"/>
  
  <!-- Floating AI particles -->
  <circle cx="24" cy="32" r="2" fill="url(#gradient3)" opacity="0.6"/>
  <circle cx="104" cy="24" r="1.5" fill="url(#gradient2)" opacity="0.6"/>
  <circle cx="112" cy="64" r="2" fill="url(#gradient1)" opacity="0.6"/>
  <circle cx="16" cy="96" r="1.5" fill="url(#gradient3)" opacity="0.6"/>
</svg>
