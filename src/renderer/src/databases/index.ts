import { FileMetadata, KnowledgeItem, QuickPhrase, TranslateHistory } from '@renderer/types'
// Import necessary types for blocks and new message structure
import type { Message as NewMessage, MessageBlock } from '@renderer/types/newMessage'
import { <PERSON><PERSON> } from 'dexie'

import { upgradeToV5, upgradeToV7, upgradeToV8 } from './upgrades'

// 数据库实例
let dbInstance: Dexie & { 
  files: any,
  topics: any,
  settings: any,
  knowledge_notes: any,
  translate_history: any,
  quick_phrases: any,
  message_blocks: any
} | null = null

// 数据库初始化状态
let dbInitialized = false
let dbInitPromise: Promise<void> | null = null

// 懒加载数据库初始化
function initDatabase() {
  if (dbInitialized) {
    return Promise.resolve()
  }
  
  if (dbInitPromise) {
    return dbInitPromise
  }

  dbInitPromise = new Promise((resolve, reject) => {
    try {
      // 创建数据库实例
      const database = new Dexie('CherryStudio') as any

      // 配置数据库版本和表结构（异步进行）
      setTimeout(() => {
        try {
          configureDatabase(database)
          dbInstance = database
          dbInitialized = true
          resolve()
        } catch (error) {
          console.error('Database configuration failed:', error)
          reject(error)
        }
      }, 0)
    } catch (error) {
      console.error('Database initialization failed:', error)
      reject(error)
    }
  })
  
  return dbInitPromise
}

// 配置数据库版本和表结构
function configureDatabase(database: Dexie) {
  database.version(1).stores({
    files: 'id, name, origin_name, path, size, ext, type, created_at, count'
  })
  
  database.version(2).stores({
    files: 'id, name, origin_name, path, size, ext, type, created_at, count',
    topics: '&id, messages',
    settings: '&id, value'
  })
  
  database.version(3).stores({
    files: 'id, name, origin_name, path, size, ext, type, created_at, count',
    topics: '&id, messages',
    settings: '&id, value',
    knowledge_notes: '&id, baseId, type, content, created_at, updated_at'
  })
  
  database.version(4).stores({
    files: 'id, name, origin_name, path, size, ext, type, created_at, count',
    topics: '&id, messages',
    settings: '&id, value',
    knowledge_notes: '&id, baseId, type, content, created_at, updated_at',
    translate_history: '&id, sourceText, targetText, sourceLanguage, targetLanguage, createdAt'
  })
  
  database.version(5)
    .stores({
      files: 'id, name, origin_name, path, size, ext, type, created_at, count',
      topics: '&id, messages',
      settings: '&id, value',
      knowledge_notes: '&id, baseId, type, content, created_at, updated_at',
      translate_history: '&id, sourceText, targetText, sourceLanguage, targetLanguage, createdAt'
    })
    .upgrade((tx: any) => upgradeToV5(tx))

  database.version(6).stores({
    files: 'id, name, origin_name, path, size, ext, type, created_at, count',
    topics: '&id, messages',
    settings: '&id, value',
    knowledge_notes: '&id, baseId, type, content, created_at, updated_at',
    translate_history: '&id, sourceText, targetText, sourceLanguage, targetLanguage, createdAt',
    quick_phrases: 'id'
  })
  
  // --- NEW VERSION 7 ---
  database.version(7)
    .stores({
      // Re-declare all tables for the new version
      files: 'id, name, origin_name, path, size, ext, type, created_at, count',
      topics: '&id', // Correct index for topics
      settings: '&id, value',
      knowledge_notes: '&id, baseId, type, content, created_at, updated_at',
      translate_history: '&id, sourceText, targetText, sourceLanguage, targetLanguage, createdAt',
      quick_phrases: 'id',
      message_blocks: 'id, messageId, file.id' // Correct syntax with comma separator
    })
    .upgrade((tx: any) => upgradeToV7(tx))

  database.version(8)
    .stores({
      // Re-declare all tables for the new version
      files: 'id, name, origin_name, path, size, ext, type, created_at, count',
      topics: '&id', // Correct index for topics
      settings: '&id, value',
      knowledge_notes: '&id, baseId, type, content, created_at, updated_at',
      translate_history: '&id, sourceText, targetText, sourceLanguage, targetLanguage, createdAt',
      quick_phrases: 'id',
      message_blocks: 'id, messageId, file.id' // Correct syntax with comma separator
    })
    .upgrade((tx: any) => upgradeToV8(tx))
}

// 获取数据库实例的代理对象
let isInitializing = false
let initPromise: Promise<void> | null = null

export const db = new Proxy({} as NonNullable<typeof dbInstance>, {
  get(target, prop) {
    if (dbInitialized && dbInstance) {
      return dbInstance[prop as keyof typeof dbInstance]
    }
    
    if (!isInitializing && !initPromise) {
      isInitializing = true
      initPromise = initDatabase().catch(console.error)
    }
    
    // 在Web环境中，我们需要更宽松的处理
    if (typeof window !== 'undefined' && (window as any).__WEB_VERSION__) {
      console.warn('Database not ready, returning mock object for:', prop)
      // 返回一个mock对象，避免崩溃
      return {
        get: () => Promise.resolve(null),
        put: () => Promise.resolve(),
        add: () => Promise.resolve(),
        update: () => Promise.resolve(),
        delete: () => Promise.resolve(),
        clear: () => Promise.resolve(),
        toArray: () => Promise.resolve([]),
        where: () => ({
          equals: () => ({
            toArray: () => Promise.resolve([])
          }),
          anyOf: () => ({
            toArray: () => Promise.resolve([])
          })
        })
      }
    }
    
    throw new Error('Database not initialized yet. Please wait for initialization to complete.')
  }
})

// 导出初始化函数供外部调用
export { initDatabase }

export default db
