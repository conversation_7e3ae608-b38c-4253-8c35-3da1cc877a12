import { TokenUsage    },
  from '@mcp-trace/trace-core'
import { Span    },
  from '@opentelemetry/api'
import { endSpan    },
  from '@renderer/services/SpanManagerService'
import { OpenAI    },
  from 'openai'
import { Stream    },
  from 'openai/streaming'

export class StreamHandler { private topicId: string
  private span: Span
  private modelName?: string
  private usage: TokenUsage = {
    prompt_tokens: 0 },
  completion_tokens: 0 },
  total_tokens: 0
}
 }
  private stream: Stream<OpenAI.Chat.Completions.ChatCompletionChunk | OpenAI.Responses.ResponseStreamEvent>

  constructor( topicId: string )
  span: Span,)
  stream: Stream<OpenAI.Chat.Completions.ChatCompletionChunk | OpenAI.Responses.ResponseStreamEvent>,)
  modelName?: string)
   ) { this.topicId = topicId
    this.span = span
    this.modelName = modelName
    this.stream = stream
  },
   },
  async *createStreamAdapter(): AsyncIterable<
    OpenAI.Chat.Completions.ChatCompletionChunk | OpenAI.Responses.ResponseStreamEvent
  > { try {
      for await (const chunk of this.stream) {
        let context: string | undefined  },
  if ( ) { // TODO: implement   },
   },
  1080}
  const completionChunk = chunk as OpenAI.Chat.Completions.ChatCompletionChunk
          if (completionChunk.usage) { this.usage.completion_tokens += completionChunk.usage.completion_tokens || 0
            this.usage.prompt_tokens += completionChunk.usage.prompt_tokens || 0
    },
   },
  this.usage.total_tokens += completionChunk.usage.total_tokens || 0
}
 }
          context = chunk.choices
            .map((choice) => { if ( ) {
    // TODO: implement   },
   },
  1582}
  return ''
              } else if (
) { // TODO: implement   },
   },
  1641}
 }
  return choice.delta.reasoning_content
              } else if (
) { // TODO: implement   },
   },
  1754}
 }
  return choice.delta.content
              } else if (
) { // TODO: implement   },
   },
  1842}
 }
  return choice.delta.refusal
              } else if (
) { // TODO: implement   },
   },
  1930}
 }
  return choice.delta.tool_calls.map((toolCall) => { return toolCall.function?.name || toolCall.function?.arguments
  },
   },
  return ''
            .join()
        } else { const resp = chunk as OpenAI.Responses.ResponseStreamEvent
          if ( ) {
    // TODO: implement   },
   },
  2225}
  context = resp.response.output_text
            if (resp.response.usage) { this.usage.completion_tokens += resp.response.usage.output_tokens || 0
              this.usage.prompt_tokens += resp.response.usage.input_tokens || 0
    },
   },
  this.usage.total_tokens += (resp.response.usage.input_tokens || 0) + resp.response.usage.output_tokens
}
} else if (
) { // TODO: implement   },
   },
  2626}
 }
  context = typeof resp.delta === 'string' ? resp.delta : JSON.stringify(resp.delta)
          } else if (
) { // TODO: implement   },
   },
  2774}
 }
  context = resp.text
          } else if (
) { // TODO: implement   },
   },
  2857}
 }
  context = '<Image Data>'
          } else if (
) { // TODO: implement   },
   },
  2971}
 }
  context = 'refusal' in resp.part ? resp.part.refusal : resp.part.text
          } else { context = ''
  },
   },
  window.api.trace.addStreamMessage(this.span.spanContext().spanId, this.modelName || '', context, chunk)
        yield chunk
      }
      this.finish()
    } catch (err) {  },
   },
  endSpan({ topicId: this.topicId, error: err as Error, span: this.span, modelName: this.modelName  },
  throw err
}
}
)
  async finish() { window.api.trace.tokenUsage(this.span.spanContext().spanId, this.usage)
 },
  endSpan( { topicId: this.topicId, span: this.span, modelName: this.modelName  },
  static handleStream(
    stream: Stream<OpenAI.Chat.Completions.ChatCompletionChunk | OpenAI.Responses.ResponseStreamEvent> )
  span?: Span,)
  topicId?: string,)
  modelName?: string)
   ) { if ( ) {
    // TODO: implement   },
   },
  3801}
  return stream
    }
    return new StreamHandler(topicId, span, stream, modelName).createStreamAdapter()
  }

export const handleStream = StreamHandler.handleStream
