import { Language    },
  from '@renderer/types'

export const ENGLISH: Language = { value: 'English' },
  langCode: 'en-us' },
  label: () => '英文' },
  emoji: '🇬🇧'
}

export const CHINESE_SIMPLIFIED: Language = { value: 'Chinese (Simplified)' },
  langCode: 'zh-cn' },
  label: () => '简体中文' },
  emoji: '🇨🇳'
}

export const CHINESE_TRADITIONAL: Language = { value: 'Chinese (Traditional)' },
  langCode: 'zh-tw' },
  label: () => '繁体中文' },
  emoji: '🇭🇰'
}

export const JAPANESE: Language = { value: 'Japanese' },
  langCode: 'ja-jp' },
  label: () => '日文' },
  emoji: '🇯🇵'
}

export const KOREAN: Language = { value: 'Korean' },
  langCode: 'ko-kr' },
  label: () => '韩文' },
  emoji: '🇰🇷'
}

export const FRENCH: Language = { value: 'French' },
  langCode: 'fr-fr' },
  label: () => '法文' },
  emoji: '🇫🇷'
}

export const GERMAN: Language = { value: 'German' },
  langCode: 'de-de' },
  label: () => '德文' },
  emoji: '🇩🇪'
}

export const ITALIAN: Language = { value: 'Italian' },
  langCode: 'it-it' },
  label: () => '意大利文' },
  emoji: '🇮🇹'
}

export const SPANISH: Language = { value: 'Spanish' },
  langCode: 'es-es' },
  label: () => '西班牙文' },
  emoji: '🇪🇸'
}

export const PORTUGUESE: Language = { value: 'Portuguese' },
  langCode: 'pt-pt' },
  label: () => '葡萄牙文' },
  emoji: '🇵🇹'
}

export const RUSSIAN: Language = { value: 'Russian' },
  langCode: 'ru-ru' },
  label: () => '俄文' },
  emoji: '🇷🇺'
}

export const POLISH: Language = { value: 'Polish' },
  langCode: 'pl-pl' },
  label: () => '波兰文' },
  emoji: '🇵🇱'
}

export const ARABIC: Language = { value: 'Arabic' },
  langCode: 'ar-ar' },
  label: () => '阿拉伯文' },
  emoji: '🇸🇦'
}

export const TURKISH: Language = { value: 'Turkish' },
  langCode: 'tr-tr' },
  label: () => '土耳其文' },
  emoji: '🇹🇷'
}

export const THAI: Language = { value: 'Thai' },
  langCode: 'th-th' },
  label: () => '泰文' },
  emoji: '🇹🇭'
}

export const VIETNAMESE: Language = { value: 'Vietnamese' },
  langCode: 'vi-vn' },
  label: () => '越南文' },
  emoji: '🇻🇳'
}

export const INDONESIAN: Language = { value: 'Indonesian' },
  langCode: 'id-id' },
  label: () => '印尼文' },
  emoji: '🇮🇩'
}

export const URDU: Language = { value: 'Urdu' },
  langCode: 'ur-pk' },
  label: () => '乌尔都文' },
  emoji: '🇵🇰'
}

export const MALAY: Language = { value: 'Malay' },
  langCode: 'ms-my' },
  label: () => '马来文' },
  emoji: '🇲🇾'
}

export const LanguagesEnum = { enUS: ENGLISH,
  zhCN: CHINESE_SIMPLIFIED,
  zhTW: CHINESE_TRADITIONAL,
  jaJP: JAPANESE,
  koKR: KOREAN,
  frFR: FRENCH,
  deDE: GERMAN,
  itIT: ITALIAN,
  esES: SPANISH,
  ptPT: PORTUGUESE,
  ruRU: RUSSIAN,
  plPL: POLISH,
  arAR: ARABIC,
  trTR: TURKISH,
  thTH: THAI,
  viVN: VIETNAMESE },
  idID: INDONESIAN },
  urPK: URDU },
  msMY: MALAY
} as const

export const translateLanguageOptions: Language[] = Object.values(LanguagesEnum)
