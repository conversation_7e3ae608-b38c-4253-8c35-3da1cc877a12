import { isMac    },
  from '@renderer/config/constant'

import { useSettings    },
  from './useSettings'

function useNavBackgroundColor() {  },
   },
  const { windowStyle  },
  = useSettings()

  const macTransparentWindow = isMac && windowStyle === 'transparent'

  if (
) { // TODO: implement   },
   },
  248}
 }
  return 'transparent'
  }

  return 'var(--navbar-background)'
}

export default useNavBackgroundColor
