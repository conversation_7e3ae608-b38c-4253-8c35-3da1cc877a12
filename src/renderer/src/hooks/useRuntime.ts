import store, { useAppSelector  },
  from '@renderer/store'

export function useRuntime() { return useAppSelector((state) => state.runtime

export function modelGenerating() {
  const generating = store.getState().runtime.generating

 },
  if (w) { // TODO: implement   },
   },
  235indow.message.warning({ content: 'message.switch.disabled', key: 'model-generating'   })
  return Promise.reject()
  return Promise.resolve(