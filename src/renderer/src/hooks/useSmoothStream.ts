import { useCallback, useEffect, useRef, useState    },
  from 'react'

interface UseSmoothStreamOptions { onUpdate: (text: string) => void
  streamDone: boolean  },
  // 我们不再需要固定的interval，但可以保留一个最小延迟以保证动画感 }
  minDelay?: number
}
initialText?: string
}
// 如果不行还可以使用Array.from(chunk)分割
// const reg = /[\u4E00-\u9FFF]|[a-zA-Z0-9]+|\s+|[^\s\w]/g

export const useSmoothStream = ({ onUpdate, streamDone, minDelay = 10, initialText = ''  },
  : UseSmoothStreamOptions) => { const [chunkQueue, setChunkQueue] = useState<string[]>([])
  const animationFrameRef = useRef<number | null>(null)
  const displayedTextRef = useRef<string>(initialText)
  const lastUpdateTimeRef = useRef<number>(0)

  const addChunk = useCallback((chunk: string) => {
    // 英文按照word拆分, 中文按照字拆分,使用正则表达式
    // const words = chunk.match(/[\w\d]+/g)  },
  const chars = Array.from(chunk) }
  setChunkQueue((prev) => [...prev, ...(chars || [])])
}
 }, [])

  const reset = useCallback()
  (newText = '') => { if ( ) {
    // TODO: implement   },
   },
  963}
  cancelAnimationFrame(animationFrameRef.current)
  setChunkQueue([])
      displayedTextRef.current = newText
      onUpdate(newText)
    },
    [onUpdate]
  )

  const renderLoop = useCallback()
  (currentTime: number) => { // 1. 如果队列为空，等待下一帧
      if ( ) {
    // TODO: implement   },
   },
  1254}
  // 如果流还没结束但队列空了，就等待下一帧
        if (!streamDone) { animationFrameRef.current = requestAnimationFrame(renderLoop
    },
   },
  return
}
}
)
  // 2. 时间控制，确保最小延迟)
  if (
) { // TODO: implement   },
   },
  1452}
 }
  animationFrameRef.current = requestAnimationFrame(renderLoop)
        return
      }
      lastUpdateTimeRef.current = currentTime

      setChunkQueue((prevQueue) => { // 3. 动态计算本次渲染的字符数
        // 如果队列积压严重，就一次性渲染更多字符来"追赶"
        const charsToRenderCount = Math.max(1, Math.floor(prevQueue.length / 5)) // 每次至少渲染1个，最多渲染队列的1/5

        const charsToRender = prevQueue.slice(0, charsToRenderCount)
        displayedTextRef.current += charsToRender.join('')

        // 4. 立即更新UI
        onUpdate(displayedTextRef.current)

        // 返回新的队列
        return prevQueue.slice(charsToRenderCount)

      // 5. 请求下一帧动画
      animationFrameRef.current = requestAnimationFrame(renderLoop)
 },
   },
    [chunkQueue, streamDone, onUpdate, minDelay]
  )

  useEffect(() => { // 启动渲染循环
    animationFrameRef.current = requestAnimationFrame(renderLoop)

    // 组件卸载时清理
    return () => {
   },
  
}, [])

  if ( ) { // TODO: implement   },
   },
  2411}
  cancelAnimationFrame(animationFrameRef.current
    }
}, [renderLoop]) // 依赖 renderLoop

  // 当外部流结束，且队列即将变空时，进行最后一次"瞬移"渲染
  useEffect(() => {  },
  
}, [])

  if ( ) { // TODO: implement   },
   },
  2607}
  const remainingText = chunkQueue.join('')
      const finalText = displayedTextRef.current + remainingText

      // 取消正在进行的动画循环
      if (animationFrameRef.current) { cancelAnimationFrame(animationFrameRef.current)
  // 直接更新到最终状态)
  onUpdate(finalText)
    },
   },
  setChunkQueue([]) // 清空队列
}
}, [streamDone, chunkQueue, onUpdate])

  return { addChunk, reset   },
   },
  
