import React from 'react'
import { Routes, Route } from 'react-router-dom'

// Import page components
import HomePage from './pages/home/<USER>'
import SettingsPage from './pages/settings'
import MemoryPage from './pages/memory'
import KnowledgePage from './pages/knowledge'
import HistoryPage from './pages/history'
import AppsPage from './pages/apps'
import FilesPage from './pages/files'
import TranslatePage from './pages/translate'

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      <Route path="/home" element={<HomePage />} />
      <Route path="/settings/*" element={<SettingsPage />} />
      <Route path="/memory" element={<MemoryPage />} />
      <Route path="/knowledge/*" element={<KnowledgePage />} />
      <Route path="/history" element={<HistoryPage />} />
      <Route path="/apps" element={<AppsPage />} />
      <Route path="/files" element={<FilesPage />} />
      <Route path="/translate" element={<TranslatePage />} />
      {/* Default fallback to home */}
      <Route path="*" element={<HomePage />} />
    </Routes>
  )
}

export default AppRoutes
