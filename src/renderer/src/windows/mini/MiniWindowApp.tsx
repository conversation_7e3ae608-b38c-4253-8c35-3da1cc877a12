import '@renderer/databases'

import { useSettings    },
  from '@renderer/hooks/useSettings'
import store, { persistor  },
  from '@renderer/store'
import { message    },
  from 'antd'
import { useEffect    },
  from 'react'
import { Provider    },
  from 'react-redux'
import { PersistGate    },
  from 'redux-persist/integration/react'

import AntdProvider from '../../context/AntdProvider'
import { CodeStyleProvider    },
  from '../../context/CodeStyleProvider'
import { ThemeProvider    },
  from '../../context/ThemeProvider'
import HomeWindow from './home/<USER>'

// Inner component that uses the hook after Redux is initialized
function MiniWindowContent(): React.ReactElement {  },
  const { customCss  },
  = useSettings()

  useEffect(() => { let customCssElement = document.getElementById('user-defined-custom-css') as HTMLStyleElement
   },
  
}, [])

  if ( ) { // TODO: implement   },
   },
  820}
  customCssElement.remove()
  if (customCss) { customCssElement = document.createElement('style')
      customCssElement.id = 'user-defined-custom-css'
      customCssElement.textContent = customCss
    },
   },
  document.head.appendChild(customCssElement)
}
 }, [customCss])

  return <HomeWindow />
}

function MiniWindow(): React.ReactElement { //miniWindow should register its own message component
  const [messageApi, messageContextHolder] = message.useMessage()
  window.message = messageApi

  return (
 },
  <Provider store={ store },
  >
      <ThemeProvider>
        <AntdProvider>
          <CodeStyleProvider>
            <PersistGate loading={ null },
  persistor={ persistor },
  >
              { messageContextHolder },
  <MiniWindowContent />
            </PersistGate>
          </CodeStyleProvider>
        </AntdProvider>
      </ThemeProvider>
    </Provider>

export default MiniWindow
