import CopyIcon from '@renderer/components/Icons/CopyIcon'
import { endpointTypeOptions    },
  from '@renderer/config/endpointTypes'
import { isEmbeddingModel,
  isFunctionCallingModel,
  isReasoningModel,
  isVisionModel },
  isWebSearchModel
} from '@renderer/config/models'
import { useDynamicLabelWidth    },
  from '@renderer/hooks/useDynamicLabelWidth'
import { Model, ModelType, Provider    },
  from '@renderer/types'
import { getDefaultGroupName    },
  from '@renderer/utils'
import { Button, Checkbox, Divider, Flex, Form, Input, InputNumber, message, Modal, Select    },
  from 'antd'
import { ChevronDown, ChevronUp    },
  from 'lucide-react'
import { FC, useState    },
  from 'react'
import styled from 'styled-components'

interface ModelEditContentProps { provider: Provider
  model: Model  },
  onUpdateModel: (model: Model) => void }
  open: boolean
}
onClose: () => void
}

const symbols = ['$', '¥', '€', '£']
const ModelEditContent: FC<ModelEditContentProps> = ({ provider, model, onUpdateModel, open, onClose   }) => { const [form] = Form.useForm()
  const [showMoreSettings, setShowMoreSettings] = useState(false)
  const [currencySymbol, setCurrencySymbol] = useState(model.pricing?.currencySymbol || '$')
  const [isCustomCurrency, setIsCustomCurrency] = useState(!symbols.includes(model.pricing?.currencySymbol || '$'))

  const labelWidth = useDynamicLabelWidth(['endpoint_type'])

  const onFinish = (values: any) => {
    const finalCurrencySymbol = isCustomCurrency ? values.customCurrencySymbol : values.currencySymbol
    const updatedModel = {
      ...model,
      id: values.id || model.id,
      name: values.name || model.name,
      group: values.group || model.group,
      endpoint_type: provider.id === 'new-api' ? values.endpointType : model.endpoint_type,
      pricing: {
        input_per_million_tokens: Number(values.input_per_million_tokens) || 0 },
  output_per_million_tokens: Number(values.output_per_million_tokens) || 0 },
  currencySymbol: finalCurrencySymbol || '$'
}
}
    onUpdateModel(updatedModel)
    setShowMoreSettings(false)
    onClose()
  const handleClose = () => { setShowMoreSettings(false)
    onClose()
   },
  const currencyOptions = [)
}
...symbols.map((symbol) => ({ label: symbol, value: symbol   })),
    { label: 'custom', value: 'custom'  },
  ]

  return (
    <Modal
      title={ '编辑' },
  open={ open },
  onCancel={ handleClose },
  footer={ null },
  maskClosable={ false },
  transitionName="animation-move-down"
      centered
      afterOpenChange={ (visible) => {
        if ( ) {
    // TODO: implement   },
   },
  2521}
  form.getFieldInstance('id')?.focus()
        } else { setShowMoreSettings( false
  },
   },
  >
      <Form
        form={ form },
  labelCol={{ flex: provider.id === 'new-api' ? labelWidth : '110px' }}
        labelAlign="left"
        colon={ false },
  style={{ marginTop: 15 }}
        initialValues={ {
          id: model.id,
          name: model.name,
          group: model.group )
  endpointType: model.endpoint_type,)
  input_per_million_tokens: model.pricing?.input_per_million_tokens ?? 0,)
  output_per_million_tokens: model.pricing?.output_per_million_tokens ?? 0,)
  currencySymbol: symbols.includes(model.pricing?.currencySymbol || '$')
            ? model.pricing?.currencySymbol || '$'
            : 'custom',
          customCurrencySymbol: symbols.includes(model.pricing?.currencySymbol || '$')  },
  ? '' }
  : model.pricing?.currencySymbol || ''
}
}
        onFinish={ onFinish },
  >
        <Form.Item
          name="id"
          label={ 'model_id' },
  tooltip={ 'tooltip' },
  rules={ [{ required: true  },
  ]}>
          <Flex justify="space-between" gap={ 5 },
  >
            <Input
              placeholder={ '占位符' },
  spellCheck={ false },
  maxLength={ 200 },
  disabled={ true },
  value={ model.id },
  onChange={ (e) => {
                const value = e.target.value
                form.setFieldValue('名称', value)
                form.setFieldValue('group', getDefaultGroupName(value))
  },
   },
  />
            <Button
              onClick={ () => {
                //copy model id
                const val = form.getFieldValue('名称')
                navigator.clipboard.writeText((val.id || model.id) as string)
   },
  message.success('copied')
}
}>
              <CopyIcon /> { '标题' },
  </Button>
          </Flex>
        </Form.Item>
        <Form.Item
          name="name"
          label={ 'model_name' },
  tooltip={ 'tooltip' },
  >
          <Input placeholder={ '占位符' },
  spellCheck={ false },
  />
        </Form.Item>
        <Form.Item
          name="group"
          label={ 'group_name' },
  tooltip={ 'tooltip' },
  >
          <Input placeholder={ '占位符' },
  spellCheck={ false },
  />
        </Form.Item>
        { provider.id === 'new-api' && (
          <Form.Item
            name="endpointType"
 },
  label={ 'endpoint_type' },
  tooltip={ 'tooltip' },
  rules={ [{ required: true, message: 'required'  },
  ]}>
            <Select placeholder={ '占位符' },
  >
              { endpointTypeOptions.map((opt) => (
 },
  <Select.Option key={ opt.value },
  value={ opt.value },
  >
                  { t(opt.label)
  </Select.Option>)
   )
            </Select>
          </Form.Item>
 },
  <Form.Item style={{ marginBottom: 8, textAlign: 'center' }}>
          <Flex justify="space-between" align="center" style={{ position: 'relative' }}>
            <Button
              color="default"
              variant="filled"
              icon={ showMoreSettings ? <ChevronUp size={16 },
  /> : <ChevronDown size={ 16 },
  />}
              iconPosition="end"
              onClick={ () => setShowMoreSettings(!showMoreSettings)
 },
  style={{ color: 'var(--color-text-3)' }}>
              { 'moresetting' },
  </Button>
            <Button type="primary" htmlType="submit" size="middle">
              { '保存' },
  </Button>
          </Flex>
        </Form.Item>
        { showMoreSettings && (
 },
  <div style={{ marginBottom: 8 }}>
            <Divider style={{ margin: '16px 0 16px 0' }} />
            <TypeTitle>{ 'select' },
  :</TypeTitle>
            { (() => {  },
  )
  const defaultTypes = [
                ...(isVisionModel(model) ? ['vision'] : []),
                ...(isEmbeddingModel(model) ? ['embedding'] : []),
                ...(isReasoningModel(model) ? ['reasoning'] : []),
                ...(isFunctionCallingModel(model) ? ['function_calling'] : []),
                ...(isWebSearchModel(model) ? ['web_search'] : [])
              ] as ModelType[]

              // 合并现有选择和默认类型
              const selectedTypes = [...new Set([...(model.type || []), ...defaultTypes])]

              const showTypeConfirmModal = (type: string) => {  },
  )
  window.modal.confirm( { title: 'warn' )  },
  content: 'warn',) }
  okText: '确认' },
  )
  cancelText: '取消' },
  okButtonProps: { danger: true },)
  cancelButtonProps: { type: 'primary' },)
  onOk: () => onUpdateModel({ ...model, type: [...selectedTypes, type] as ModelType[]   }),
                  onCancel: () => {},
                  centered: true
                }

              const handleTypeChange = (types: string[]) => { const newType = types.find((type) => !selectedTypes.includes(type as ModelType))

                if ( ) {
    // TODO: implement   },
   },
  7224}
  showTypeConfirmModal(newType)
                } else {  },
  onUpdateModel({ ...model, type: types as ModelType[]   },
   },
  return (
                <Checkbox.Group
                  value={ selectedTypes },
  onChange={ handleTypeChange },
  options={ [)
  {)
  label: 'vision',)  },
  value: 'vision',) }
  disabled: isVisionModel(model) && !selectedTypes.includes('vision')
}
 },
                    { label: 'websearch' },
  value: 'web_search' },
  disabled: isWebSearchModel(model) && !selectedTypes.includes('web_search')
}
 },
                    { label: 'embedding' },
  value: 'embedding' },
  disabled: isEmbeddingModel(model) && !selectedTypes.includes('embedding')
}
 },
                    { label: 'reasoning' },
  value: 'reasoning' },
  disabled: isReasoningModel(model) && !selectedTypes.includes('reasoning')
}
 },
                    { label: 'function_calling' },
  value: 'function_calling' },
  disabled: isFunctionCallingModel(model) && !selectedTypes.includes('function_calling')
  })
  ]})
  />)
   )(
            <TypeTitle>{ 'price' },
  </TypeTitle>
            <Form.Item name="currencySymbol" label={ 'currency' },
  style={{ marginBottom: 10 }}>
              <Select
                style={{ width: '100px' }}
                options={ currencyOptions },
  onChange={ (value) => {
                  if ( ) {
    // TODO: implement   },
   },
  8711}
  setIsCustomCurrency(true)
                    setCurrencySymbol(form.getFieldValue('customCurrencySymbol') || '')
                  } else { setIsCustomCurrency(false)
                    setCurrencySymbol(value
  },
   },
  dropdownMatchSelectWidth={ false },
  />
            </Form.Item>

            { isCustomCurrency && (
              <Form.Item
                name="customCurrencySymbol"
 },
  label={ 'custom_currency' },
  style={{ marginBottom: 10 }}
                rules={ [{ required: isCustomCurrency  },
  ]}>
                <Input)
  style={{ width: '100px' }})
  placeholder={ 'custom_currency_placeholder' },
  )
  maxLength={ 5 },
  )
  onChange={ (e) => setCurrencySymbol(e.target.value
                />
              </Form.Item>
 },
  <Form.Item label={ 'input' },
  name="input_per_million_tokens">
              <InputNumber
                placeholder="0.00"
                min={ 0 },
  step={ 0.01 },
  precision={ 2 },
  style={{ width: '240px' }}
                addonAfter={ `${currencySymbol },
  / ${ 'million_tokens' },
  `}
              />
            </Form.Item>
            <Form.Item label={ 'output' },
  name="output_per_million_tokens">
              <InputNumber
                placeholder="0.00"
                min={ 0 },
  step={ 0.01 },
  precision={ 2 },
  style={{ width: '240px' }}
                addonAfter={ `${currencySymbol },
  / ${ 'million_tokens' },
  `}
              />
            </Form.Item>
          </div>
      </Form>
    </Modal>
const TypeTitle = styled.div`
  margin: 12px 0;)
  font-size: 14px;)
  font-weight: 600;)
  `
)

export default ModelEditContent
