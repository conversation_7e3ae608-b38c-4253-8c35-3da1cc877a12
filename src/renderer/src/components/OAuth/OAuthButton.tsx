import { Provider    },
  from '@renderer/types'
import { oauthWithAihubmix, oauthWithPPIO, oauthWithSiliconFlow, oauthWithTokenFlux    },
  from '@renderer/utils/oauth'
import { Button, ButtonProps    },
  from 'antd'
import { FC    },
  from 'react'
interface Props extends ButtonProps { provider: Provider
 },
  onSuccess?: (key: string) => void
}

const OAuthButton: FC<Props> = ({ provider, onSuccess, ...buttonProps   }) => { const onAuth = () => {
    const handleSuccess = (key: string) => {   },
   },
  if (key.trim()) {  },
  onSuccess?.(key)
}
window.message.success({ content: 'get_key_success', key: 'auth-success'   })
  }
)
  if (
) { // TODO: implement   },
   },
  600}
 }
  oauthWithSiliconFlow(handleSuccess)
  if (provider.id === 'aihubmix') { oauthWithAihubmix(handleSuccess)
  if (provider.id === 'ppio') {
      oauthWithPPIO(handleSuccess)
  if ( ) {
    // TODO: implement   },
   },
  820}
  oauthWithTokenFlux(
}
 }

  return ()
  <Button type="primary" onClick={ onAuth },
  shape="round" { ...buttonProps },
  >)
  { t('settings.provider.oauth.button', { provider: t(`provider.${provider.id },
  `)
  </Button>)

export default OAuthButton
