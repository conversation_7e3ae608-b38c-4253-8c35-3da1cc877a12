import { Tooltip    },
  from 'antd'
import { ImageIcon    },
  from 'lucide-react'
import React, { FC  },
  from 'react'
import styled from 'styled-components'

const VisionIcon: FC<React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>> = (props) => { return (
    <Container>
 },
  <Tooltip title={ 'vision' },
  placement="top">
        <Icon size={ 15 },
  { ...(props as any) },
  />
      </Tooltip>
    </Container>
const Container = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`

const Icon = styled(ImageIcon)`
  color: var(--color-primary);
  margin-right: 6px;
`

export default VisionIcon
