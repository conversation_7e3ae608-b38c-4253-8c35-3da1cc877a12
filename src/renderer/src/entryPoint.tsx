import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App'
import { initFastStart } from './utils/fastStart'
import { perf } from './utils/perf'

// 启动性能监控
perf.mark('entry-point-start')

// 快速启动初始化
async function startApp() {
  try {
    // 初始化快速启动
    await initFastStart()
    perf.mark('fast-start-complete')

    // 异步加载初始化脚本
    import('./init').catch(error => {
      console.warn('Init script failed:', error)
    })

    // 渲染应用
    const root = createRoot(document.getElementById('root') as HTMLElement)
    root.render(<App />)

    // 性能报告
    setTimeout(() => {
      const report = perf.getReport()
      console.log('🚀 启动性能报告:', report)
      console.log(`📊 总启动时间: ${perf.getTotalTime().toFixed(2)} ms`)
    }, 1000)

  } catch (error) {
    console.error('App startup failed:', error)
    // 降级启动
    const root = createRoot(document.getElementById('root') as HTMLElement)
    root.render(<App />)
  }
}

// 启动应用
startApp()
