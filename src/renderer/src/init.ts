import { loggerService } from '@logger'
import { webTraceService } from './services/WebTraceService'
import { storeSyncService } from './services/StoreSyncService'

const logger = loggerService.withContext('Init')

// 初始化KeyvStorage
try {
  if (window.electron && !window.__WEB_VERSION__) {
    // Electron环境
    window.keyv.init()
  } else {
    // Web环境提供简单的存储实现
    window.keyv = {
      get: async (key: string) => {
        try {
          const value = localStorage.getItem(key)
          return value ? JSON.parse(value) : undefined
        } catch {
          return localStorage.getItem(key)
        }
      },
      set: async (key: string, value: any) => {
        try {
          localStorage.setItem(key, JSON.stringify(value))
        } catch {
          localStorage.setItem(key, String(value))
        }
      },
      delete: async (key: string) => {
        localStorage.removeItem(key)
      },
      clear: async () => {
        localStorage.clear()
      },
      init: () => {}
    }
  }
} catch (error) {
  console.warn('KeyvStorage initialization failed in web environment:', error)
  // 为Web环境提供一个简单的存储实现
  window.keyv = {
    get: async () => null,
    set: async () => {},
    delete: async () => {},
    clear: async () => {},
    init: () => {}
  }
}

// 初始化追踪服务
try {
  webTraceService.init()
  logger.info('WebTraceService initialized')
} catch (error) {
  logger.warn('WebTraceService initialization failed:', error)
}

// 初始化存储同步服务
try {
  storeSyncService.init({
    syncList: [
      'assistants/',
      'settings/',
      'shortcuts/',
      'websearch/',
      'memory/',
      'backup/',
      'nutstore/',
      'runtime/',
      'llm/',
      'copilot/',
      'preprocess/',
      'ocr/',
      'inputTools/',
      'mcp/',
      'newMessage/',
      'messageBlock/',
      'minapps/',
      'selectionStore/'
    ]
  })
  storeSyncService.subscribe()
  logger.info('StoreSyncService initialized')
} catch (error) {
  logger.warn('StoreSyncService initialization failed:', error)
}

logger.info('Application initialization completed')
