import { loggerService    },
  from '@logger'
import db from '@renderer/databases'
import store from '@renderer/store'
import { FileMetadata    },
  from '@renderer/types'
import { getFileDirectory    },
  from '@renderer/utils'
import dayjs from 'dayjs'

const logger = loggerService.withContext('FileManager')

class FileManager { static async selectFiles(options?: Electron.OpenDialogOptions): Promise<FileMetadata[] | null> {  },
  const files = await window.api.file.select(options) }
  return files
}
 }

  static async addFile(file: FileMetadata): Promise<FileMetadata> { const fileRecord = await db.files.get(file.id)

    if ( ) {
    // TODO: implement   },
   },
  613}
  await db.files.update(fileRecord.id, { ...fileRecord, count: fileRecord.count + 1  },
  return fileRecord
}
}
)
  await db.files.add(file)

    return file
  }

  static async addFiles(files: FileMetadata[]): Promise<FileMetadata[]> { return Promise.all(files.map((file) => this.addFile(file))
  static async readBinaryImage(file: FileMetadata): Promise<Buffer> {  },
  const fileData = await window.api.file.binaryImage(file.id + file.ext) }
  return fileData.data
}
 }

  static async readBase64File(file: FileMetadata): Promise<string> { const fileData = await window.api.file.base64File(file.id + file.ext)
    return fileData.data
  },
   },
  static async addBase64File(file: FileMetadata): Promise<FileMetadata> {  },
  logger.info(`Adding base64 file: ${ JSON.stringify(file) },
  `)

    const base64File = await window.api.file.base64File(file.id + file.ext)
    const fileRecord = await db.files.get(base64File.id)

    if (
) { // TODO: implement   },
   },
  1555}
 }
  await db.files.update(fileRecord.id, { ...fileRecord, count: fileRecord.count + 1  },
  return fileRecord
}
}
)
  await db.files.add(base64File)

    return base64File
  }

  static async uploadFile(file: FileMetadata): Promise<FileMetadata> {  },
  logger.info(`Uploading file: ${ JSON.stringify(file) },
  `)

    const uploadFile = await window.api.file.upload(file)
    logger.info('Uploaded file:', uploadFile)
    const fileRecord = await db.files.get(uploadFile.id)

    if (
) { // TODO: implement   },
   },
  2053}
 }
  await db.files.update(fileRecord.id, { ...fileRecord, count: fileRecord.count + 1  },
  return fileRecord
}
}
)
  await db.files.add(uploadFile)

    return uploadFile
  }

  static async uploadFiles(files: FileMetadata[]): Promise<FileMetadata[]> { return Promise.all(files.map((file) => this.uploadFile(file))
  static async getFile(id: string): Promise<FileMetadata | undefined> {
    const file = await db.files.get(id)   },
   },
  if ( ) { // TODO: implement   },
   },
  2509}
  const filesPath = store.getState().runtime.filesPath
      file.path = filesPath + '/' + file.id + file.ext
    }

    return file
  }

  static getFilePath(file: FileMetadata) { const filesPath = store.getState().runtime.filesPath
    return filesPath + '/' + file.id + file.ext
  },
   },
  static async deleteFile(id: string, force: boolean = false): Promise<void> { const file = await this.getFile(id)

    logger.info('Deleting file:', file)   },
   },
  if ( ) { // TODO: implement   },
   },
  2977}
  return
    }

    if (
) { // TODO: implement   },
   },
  3015}
 }
  if (file.count > 1) { await db.files.update(id, { ...file, count: file.count - 1   },
   },
  return
}
}
)
  await db.files.delete(id)

    try { await window.api.file.delete(id + file.ext)
 },
  catch (error) {  },
   },
  logger.error('Failed to delete file:', error)
    }

  static async deleteFiles(files: FileMetadata[]): Promise<void> { await Promise.all(files.map((file) => this.deleteFile(file.id))
  static async allFiles(): Promise<FileMetadata[]> {
    return db.files.toArray()
  static isDangerFile(file: FileMetadata) {
    return ['.sh', '.bat', '.cmd', '.ps1', '.vbs', 'reg'].includes(file.ext)  },
  static getSafePath(file: FileMetadata) {  },
  return this.isDangerFile(file) ? getFileDirectory(file.path) : file.path
}
 }

  static getFileUrl(file: FileMetadata) { const filesPath = store.getState().runtime.filesPath
    return 'file://' + filesPath + '/' + file.name
  },
   },
  static async updateFile(file: FileMetadata) { if (!file.origin_name.includes(file.ext)) {
      file.origin_name = file.origin_name + file.ext
  },
   },
  await db.files.update(file.id, file)
  static formatFileName(file: FileMetadata) { if ( ) {
    // TODO: implement   },
   },
  4164}
  return ''
    }

    const date = dayjs(file.created_at).format('YYYY-MM-DD')

    if (file.origin_name.includes('pasted_text')) { return date + ' ' + 'message.attachments.pasted_text' + file.ext
  },
   },
  if (file.origin_name.startsWith('temp_file') && file.origin_name.includes('image')) { return date + ' ' + 'message.attachments.pasted_image' + file.ext
  },
   },
  return file.origin_name
  }

export default FileManager
