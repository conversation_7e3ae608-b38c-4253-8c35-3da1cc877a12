import { loggerService } from '@logger'
import { Middleware } from '@reduxjs/toolkit'
import { IpcChannel } from '@shared/IpcChannel'
import type { StoreSyncAction } from '@types'

const logger = loggerService.withContext('StoreSyncService')

type SyncOptions = {
  syncList: string[]
}

export class StoreSyncService {
  private static instance: StoreSyncService
  private options: SyncOptions = {
    syncList: []
  }
  private broadcastSyncRemover: (() => void) | null = null

  private constructor() {
    // Private constructor for singleton pattern
  }

  public static getInstance(): StoreSyncService {
    if (!StoreSyncService.instance) {
      StoreSyncService.instance = new StoreSyncService()
    }
    return StoreSyncService.instance
  }

  public init(options: SyncOptions): void {
    this.options = options
  }

  public createMiddleware(): Middleware {
    return (store) => (next) => (action) => {
      const result = next(action)

      if (this.shouldSyncAction(action.type)) {
        const syncAction: StoreSyncAction = {
          type: action.type,
          payload: action.payload
        }
        if (window.api?.storeSync) {
          window.api.storeSync.onUpdate(syncAction)
        }
      }

      return result
    }
  }

  private shouldSyncAction(actionType: string): boolean {
    return this.options.syncList.some((prefix) => {
      return actionType.startsWith(prefix)
    })
  }

  public subscribe(): void {
    if (this.broadcastSyncRemover || !window.api?.storeSync) {
      return
    }
    if (window.api?.storeSync) {
      this.broadcastSyncRemover = window.api.storeSync.onReceive((action: StoreSyncAction) => {
        try {
          if (window.store) {
            window.store.dispatch(action)
          }
        } catch (error) {
          logger.error('Error dispatching synced action:', error)
        }
      })
    }

    window.api.storeSync.subscribe()

    window.addEventListener('beforeunload', () => {
      this.unsubscribe()
    })
  }

  public unsubscribe(): void {
    if (window.api?.storeSync) {
      window.api.storeSync.unsubscribe()
    }
    if (this.broadcastSyncRemover) {
      this.broadcastSyncRemover()
      this.broadcastSyncRemover = null
    }
  }
}

export const storeSyncService = StoreSyncService.getInstance()
