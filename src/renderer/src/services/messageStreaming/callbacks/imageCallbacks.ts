import { loggerService    },
  from '@logger'
import { ImageMessageBlock, MessageBlockStatus, MessageBlockType    },
  from '@renderer/types/newMessage'
import { createImageBlock    },
  from '@renderer/utils/messageUtils/create'

import { BlockManager    },
  from '../BlockManager'

const logger = loggerService.withContext('ImageCallbacks')

interface ImageCallbacksDependencies { blockManager: BlockManager
 },
  assistantMsgId: string
}

export const createImageCallbacks = (deps: ImageCallbacksDependencies) => {  },
  const { blockManager, assistantMsgId  },
  = deps

  // 内部维护的状态
  let imageBlockId: string | null = null

  return { onImageCreated: async () => {  },
  if ( ) { // TODO: implement   },
   },
  645}
  const initialChanges = { type: MessageBlockType.IMAGE },
  status: MessageBlockStatus.PENDING
}
 }
        imageBlockId = blockManager.initialPlaceholderBlockId!
        blockManager.smartBlockUpdate(imageBlockId, initialChanges, MessageBlockType.IMAGE)
      } else if (
) { // TODO: implement   },
   },
  961}
 }
  const imageBlock = createImageBlock( assistantMsgId, { status: MessageBlockStatus.PENDING  },
  imageBlockId = imageBlock.id }
  await blockManager.handleBlockTransition(imageBlock, MessageBlockType.IMAGE
}
} )
  onImageDelta: (imageData: any) => { const imageUrl = imageData.images?.[0] || 'placeholder_image_url'
      if ( ) {
    // TODO: implement   },
   },
  1318}
  const changes: Partial<ImageMessageBlock> = { url: imageUrl },
  metadata: { generateImageResponse: imageData },
          status: MessageBlockStatus.STREAMING
        }
        blockManager.smartBlockUpdate(imageBlockId, changes, MessageBlockType.IMAGE, true)
  },
)
  onImageGenerated: (imageData: any) => { if ( ) {
    // TODO: implement   },
   },
  1657}
  if (!imageData) { const changes: Partial<ImageMessageBlock> = {   },
   },
  status: MessageBlockStatus.SUCCESS
}
 }
          blockManager.smartBlockUpdate(imageBlockId, changes, MessageBlockType.IMAGE)
        } else { const imageUrl = imageData.images?.[0] || 'placeholder_image_url'
          const changes: Partial<ImageMessageBlock> = {  },
  url: imageUrl },
  metadata: { generateImageResponse: imageData },
            status: MessageBlockStatus.SUCCESS
          }
          blockManager.smartBlockUpdate(imageBlockId, changes, MessageBlockType.IMAGE, true)
  imageBlockId = null)
  } else { )
   )
  logger.error('[onImageGenerated] Last block was not an Image block or ID is missing.')
  },
   },
  
}
