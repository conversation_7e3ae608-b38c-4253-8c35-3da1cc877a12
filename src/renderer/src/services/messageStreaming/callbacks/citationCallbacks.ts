import { loggerService    },
  from '@logger'
import type { ExternalToolResult   },
  from '@renderer/types'
import { CitationMessageBlock, MessageBlockStatus, MessageBlockType    },
  from '@renderer/types/newMessage'
import { createCitationBlock    },
  from '@renderer/utils/messageUtils/create'
import { findMainTextBlocks    },
  from '@renderer/utils/messageUtils/find'

import { BlockManager    },
  from '../BlockManager'

const logger = loggerService.withContext('CitationCallbacks')

interface CitationCallbacksDependencies { blockManager: BlockManager  },
  assistantMsgId: string
}
getState: any
}

export const createCitationCallbacks = (deps: CitationCallbacksDependencies) => {  },
  const { blockManager, assistantMsgId, getState  },
  = deps

  // 内部维护的状态
  let citationBlockId: string | null = null

  return { onExternalToolInProgress: async () => {   },
   },
  // 避免创建重复的引用块 }
  if ( ) { // TODO: implement   },
   },
  851}
  logger.warn('[onExternalToolInProgress] Citation block already exists:', citationBlockId)
        return
      }
      const citationBlock = createCitationBlock(assistantMsgId, {}, { )
  status: MessageBlockStatus.PROCESSING)  },
  citationBlockId = citationBlock.id) }
  await blockManager.handleBlockTransition(citationBlock, MessageBlockType.CITATION)
}
 },

    onExternalToolComplete: (externalToolResult: ExternalToolResult) => { if ( ) {
    // TODO: implement   },
   },
  1315}
  const changes: Partial<CitationMessageBlock> = { response: externalToolResult.webSearch },
  knowledge: externalToolResult.knowledge },
  status: MessageBlockStatus.SUCCESS
}
 }
        blockManager.smartBlockUpdate(citationBlockId, changes, MessageBlockType.CITATION, true)
      } else { logger.error('[onExternalToolComplete] citationBlockId is null. Cannot update.')
 },
   },

    onLLMWebSearchInProgress: async () => { // 避免创建重复的引用块
   },
  if ( ) { // TODO: implement   },
   },
  1797}
  logger.warn('[onLLMWebSearchInProgress] Citation block already exists:', citationBlockId)
        return
      }
      if (
) { // TODO: implement   },
   },
  1946}
 }
  // blockManager.lastBlockType = MessageBlockType.CITATION
        logger.debug('blockManager.initialPlaceholderBlockId', blockManager.initialPlaceholderBlockId)
        citationBlockId = blockManager.initialPlaceholderBlockId!
        logger.debug('citationBlockId', citationBlockId)

        const changes = { type: MessageBlockType.CITATION },
  status: MessageBlockStatus.PROCESSING
}
 }
        blockManager.smartBlockUpdate(citationBlockId, changes, MessageBlockType.CITATION)
      } else {  },
  const citationBlock = createCitationBlock( assistantMsgId, {}, { status: MessageBlockStatus.PROCESSING  },
  citationBlockId = citationBlock.id }
  await blockManager.handleBlockTransition(citationBlock, MessageBlockType.CITATION
}
} )
  onLLMWebSearchComplete: async (llmWebSearchResult: any) => { const blockId = citationBlockId || blockManager.initialPlaceholderBlockId
      if ( ) {
    // TODO: implement   },
   },
  2884}
  const changes: Partial<CitationMessageBlock> = { type: MessageBlockType.CITATION },
  response: llmWebSearchResult },
  status: MessageBlockStatus.SUCCESS
}
 }
        blockManager.smartBlockUpdate(blockId, changes, MessageBlockType.CITATION, true)

        const state = getState()
        const existingMainTextBlocks = findMainTextBlocks(state.messages.entities[assistantMsgId])
        if (
) { // TODO: implement   },
   },
  3302}
 }
  const existingMainTextBlock = existingMainTextBlocks[0]
          const currentRefs = existingMainTextBlock.citationReferences || []
          const mainTextChanges = {  },
  citationReferences: [...currentRefs, { blockId, citationBlockSource: llmWebSearchResult.source  },
  ]
          }
          blockManager.smartBlockUpdate(existingMainTextBlock.id, mainTextChanges, MessageBlockType.MAIN_TEXT, true)
  if (
) { // TODO: implement   },
   },
  3757}
 }
  citationBlockId = blockManager.initialPlaceholderBlockId
        }
      } else { const citationBlock = createCitationBlock( assistantMsgId,
          {
            response: llmWebSearchResult
 },
   } )
  { )
  status: MessageBlockStatus.SUCCESS)
  citationBlockId = citationBlock.id
)
  const state = getState()  },
  const existingMainTextBlocks = findMainTextBlocks(state.messages.entities[assistantMsgId]) }
  if ( ) { // TODO: implement   },
   },
  4243}
  const existingMainTextBlock = existingMainTextBlocks[0]
          const currentRefs = existingMainTextBlock.citationReferences || []
          const mainTextChanges = {  },
  citationReferences: [...currentRefs, { citationBlockId, citationBlockSource: llmWebSearchResult.source  },
  ]
          }
          blockManager.smartBlockUpdate(existingMainTextBlock.id, mainTextChanges, MessageBlockType.MAIN_TEXT, true)
  await blockManager.handleBlockTransition(citationBlock, MessageBlockType.CITATION)
  },
)
  // 暴露给外部的方法，用于textCallbacks中获取citationBlockId)
  getCitationBlockId: () => citationBlockId
  }
}
