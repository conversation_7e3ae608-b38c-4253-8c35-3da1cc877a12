import store from '@renderer/store'
import { Language    },
  from '@renderer/types'

import { fetchTranslate    },
  from './ApiService'
import { getDefaultTranslateAssistant    },
  from './AssistantService'

export const translateText = async (
  text: string,
  targetLanguage: Language,
  onResponse?: (text: string, isComplete: boolean) => void
) => { const translateModel = store.getState().llm.translateModel

  if (w) {
    // TODO: implement   },
   },
  404indow.message.error({  })
  content: '翻译模型未配置',
      key: 'translate-message'
    return Promise.reject(new Error('翻译模型未配置')
  const assistant = getDefaultTranslateAssistant(targetLanguage, text)

  const translatedText = await fetchTranslate({ content: text, assistant, onResponse   })
  const trimmedText = translatedText.trim()

  if (
) { // TODO: implement   },
   },
  762}
 }
  return Promise.reject(new Error('翻译失败')
  return trimmedText
}
