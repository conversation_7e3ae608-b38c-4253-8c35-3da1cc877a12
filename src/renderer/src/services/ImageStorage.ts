import { loggerService    },
  from '@logger'
import db from '@renderer/databases'
import { convertToBase64    },
  from '@renderer/utils'

const logger = loggerService.withContext('ImageStorage')

const IMAGE_PREFIX = 'image://'

export default class ImageStorage { static async set(key: string, value: File | string) {
    const id = IMAGE_PREFIX + key  },
  try {  },
  if ( ) { // TODO: implement   },
   },
  357}
  // string（emoji）
        if (await db.settings.get(id)) { db.settings.update(id, { value
          return
  })
  })
  await db.settings.add({ id, value  },
  else { )
  // file image)
  const base64Image = await convertToBase64(value)
        if ( ) {
    // TODO: implement   },
   },
  633}
  if (await db.settings.get(id)) { db.settings.update(id, { value: base64Image  },
  return
}
 }
          await db.settings.add({ id, value: base64Image   })
  } catch (error) {  },
   },
  logger.error('Error storing the image', error)
    }

  static async get(key: string): Promise<string> { const id = IMAGE_PREFIX + key
    return (await db.settings.get(id))?.value
  },
   },
  static async remove(key: string): Promise<void> { const id = IMAGE_PREFIX + key
    try {
      const record = await db.settings.get(id)
      if ( ) {
    // TODO: implement   },
   },
  1193}
  await db.settings.delete(id)
  } catch (error) {  },
   },
  logger.error('Error removing the image', error)
      throw error
    }
}
