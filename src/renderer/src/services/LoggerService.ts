export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

const DEFAULT_LEVEL: LogLevel = 'info'
const MAIN_LOG_LEVEL: LogLevel = 'warn'

class LoggerService {
  private level: LogLevel = DEFAULT_LEVEL
  private logToMainLevel: LogLevel = MAIN_LOG_LEVEL
  private window: string = ''
  private module: string = ''
  private context: Record<string, any> = {}
  private static instance: LoggerService

  private constructor() {
    // 在Web环境中提供默认的window source
    if (typeof window !== 'undefined' && !window.electron) {
      this.window = 'web'
    }
  }

  static getInstance(): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService()
    }
    return LoggerService.instance
  }

  withContext(module: string): LoggerService {
    const instance = new LoggerService()
    instance.module = module
    instance.level = this.level
    instance.logToMainLevel = this.logToMainLevel
    instance.window = this.window
    instance.context = { ...this.context }
    return instance
  }

  setLevel(level: LogLevel): void {
    this.level = level
  }

  setLogToMainLevel(level: LogLevel): void {
    this.logToMainLevel = level
  }

  debug(message: string, ...args: any[]): void {
    this.log('debug', message, ...args)
  }

  info(message: string, ...args: any[]): void {
    this.log('info', message, ...args)
  }

  warn(message: string, ...args: any[]): void {
    this.log('warn', message, ...args)
  }

  error(message: string, ...args: any[]): void {
    this.log('error', message, ...args)
  }

  private log(level: LogLevel, message: string, ...args: any[]): void {
    if (!this.shouldLog(level)) {
      return
    }
    
    const timestamp = new Date().toISOString()
    const prefix = `[${timestamp}] [${level.toUpperCase()}] [${this.module || 'Unknown'}]`
    
    console[level](`${prefix} ${message}`, ...args)

    // Send to main process if needed
    if (this.shouldLogToMain(level) && window.api?.logger) {
      window.api.logger.log(level, this.module, message, ...args)
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error']
    return levels.indexOf(level) >= levels.indexOf(this.level)
  }

  private shouldLogToMain(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error']
    return levels.indexOf(level) >= levels.indexOf(this.logToMainLevel)
  }
}

export const loggerService = LoggerService.getInstance()
