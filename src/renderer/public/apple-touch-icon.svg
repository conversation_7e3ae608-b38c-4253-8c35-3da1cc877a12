<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with rounded corners for iOS -->
  <rect width="180" height="180" rx="40" fill="white"/>
  
  <!-- Background circle -->
  <circle cx="90" cy="90" r="75" fill="url(#gradient1)" opacity="0.1"/>
  
  <!-- Main chat bubble -->
  <path d="M45 56 C45 45, 56 34, 67 34 L113 34 C124 34, 135 45, 135 56 L135 90 C135 101, 124 112, 113 112 L79 112 L56 130 L56 112 C45 112, 45 101, 45 90 Z" fill="url(#gradient1)"/>
  
  <!-- AI brain/neural network dots -->
  <circle cx="73" cy="67" r="4" fill="white" opacity="0.9"/>
  <circle cx="90" cy="67" r="4" fill="white" opacity="0.9"/>
  <circle cx="107" cy="67" r="4" fill="white" opacity="0.9"/>
  
  <circle cx="81" cy="84" r="3.5" fill="white" opacity="0.7"/>
  <circle cx="99" cy="84" r="3.5" fill="white" opacity="0.7"/>
  
  <!-- Neural connections -->
  <line x1="73" y1="67" x2="81" y2="84" stroke="white" stroke-width="1.5" opacity="0.5"/>
  <line x1="90" y1="67" x2="81" y2="84" stroke="white" stroke-width="1.5" opacity="0.5"/>
  <line x1="90" y1="67" x2="99" y2="84" stroke="white" stroke-width="1.5" opacity="0.5"/>
  <line x1="107" y1="67" x2="99" y2="84" stroke="white" stroke-width="1.5" opacity="0.5"/>
  
  <!-- Secondary chat bubble -->
  <path d="M101 124 C101 118, 107 112, 113 112 L141 112 C147 112, 153 118, 153 124 L153 141 C153 147, 147 153, 141 153 L124 153 L113 164 L113 153 C107 153, 101 147, 101 141 Z" fill="url(#gradient2)" opacity="0.8"/>
  
  <!-- AI indicator dots in second bubble -->
  <circle cx="124" cy="132" r="2" fill="white"/>
  <circle cx="132" cy="132" r="2" fill="white"/>
  <circle cx="140" cy="132" r="2" fill="white"/>
  
  <!-- Floating AI particles -->
  <circle cx="34" cy="45" r="3" fill="url(#gradient3)" opacity="0.6"/>
  <circle cx="146" cy="34" r="2" fill="url(#gradient2)" opacity="0.6"/>
  <circle cx="158" cy="90" r="3" fill="url(#gradient1)" opacity="0.6"/>
  <circle cx="22" cy="135" r="2" fill="url(#gradient3)" opacity="0.6"/>
</svg>
