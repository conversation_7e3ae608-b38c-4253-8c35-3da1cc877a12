<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="initial-scale=1, width=device-width" />
    <meta name="description" content="ZChat - A powerful AI assistant for producer" />
    <meta name="keywords" content="<PERSON>, Assistant, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>" />
    <title>ZChat - AI智能助手</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.svg" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="ZChat" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="ZChat - AI智能助手" />
    <meta property="og:description" content="强大的AI助手，支持多种大语言模型，提供智能对话、翻译、知识库等功能" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="ZChat - AI智能助手" />
    <meta name="twitter:description" content="强大的AI助手，支持多种大语言模型，提供智能对话、翻译、知识库等功能" />

    <!-- 预加载关键资源 -->
    <link rel="preload" href="/src/assets/images/logo.svg" as="image" />
    <link rel="preload" href="/src/assets/styles/index.scss" as="style" />
    <link rel="modulepreload" href="/src/entryPoint.tsx" />
    <link rel="modulepreload" href="/src/App.tsx" />

    <!-- DNS预解析 -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net" />

    <style>
      html,
      body {
        margin: 0;
      }

      #spinner {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.3s ease-out;
      }

      #spinner img {
        width: 100px;
        border-radius: 50px;
        animation: pulse 2s infinite;
      }

      #spinner .loading-text {
        color: white;
        font-size: 18px;
        margin-top: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.05); opacity: 0.8; }
        100% { transform: scale(1); opacity: 1; }
      }

      /* 优化初始加载 */
      body {
        overflow: hidden;
      }

      #root {
        height: 100vh;
        overflow: hidden;
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    <div id="spinner">
      <img src="/src/assets/images/logo.svg" alt="ZChat Loading" />
      <div class="loading-text">ZChat 正在启动...</div>
    </div>
    <script>
      console.time('init')
    </script>
    <script type="module" src="/src/init.ts"></script>
    <script type="module" src="/src/entryPoint.tsx"></script>
  </body>
</html>
