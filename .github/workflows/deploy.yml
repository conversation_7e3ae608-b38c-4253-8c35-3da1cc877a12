name: Deploy to EdgeOne

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: '18'

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm install --legacy-peer-deps
      
    - name: Run tests
      run: npm run test
      
    - name: Build project
      run: npm run build
      env:
        NODE_ENV: production
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dist
        path: dist/
        retention-days: 1

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: dist
        path: dist/
        
    - name: Deploy to EdgeOne Staging
      run: |
        echo "Deploying to staging environment..."
        # 这里添加实际的 EdgeOne 部署命令
        # 例如: edgeone deploy --site-id=${{ secrets.EDGEONE_STAGING_SITE_ID }} --source=dist
      env:
        EDGEONE_API_KEY: ${{ secrets.EDGEONE_API_KEY }}
        EDGEONE_SITE_ID: ${{ secrets.EDGEONE_STAGING_SITE_ID }}

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: dist
        path: dist/
        
    - name: Deploy to EdgeOne Production
      run: |
        echo "Deploying to production environment..."
        # 这里添加实际的 EdgeOne 部署命令
        # 例如: edgeone deploy --site-id=${{ secrets.EDGEONE_PRODUCTION_SITE_ID }} --source=dist
      env:
        EDGEONE_API_KEY: ${{ secrets.EDGEONE_API_KEY }}
        EDGEONE_SITE_ID: ${{ secrets.EDGEONE_PRODUCTION_SITE_ID }}
        
    - name: Notify deployment success
      run: |
        echo "✅ Production deployment completed successfully!"
        echo "🌐 Website: https://zchat.example.com"
