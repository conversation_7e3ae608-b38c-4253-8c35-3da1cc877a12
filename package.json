{"name": "cherry-studio-web", "version": "1.5.1", "private": true, "description": "A powerful AI assistant for producer - Web Version", "type": "module", "engines": {"node": ">=18.0.0"}, "workspaces": {"packages": ["packages/*"]}, "scripts": {"dev": "vite", "build": "npm run typecheck && vite build", "build:prod": "NODE_ENV=production npm run build", "preview": "vite preview", "typecheck": "tsc --noEmit -p tsconfig.json --composite false", "test": "vitest run --silent", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage --silent", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write .", "analyze": "VISUALIZER=true npm run build", "deploy": "chmod +x deploy.sh && ./deploy.sh", "deploy:dev": "chmod +x deploy.sh && ./deploy.sh dev", "deploy:staging": "chmod +x deploy.sh && ./deploy.sh staging", "deploy:prod": "chmod +x deploy.sh && ./deploy.sh production"}, "dependencies": {"@agentic/exa": "^7.3.3", "@agentic/searxng": "^7.3.3", "@agentic/tavily": "^7.3.3", "@anthropic-ai/sdk": "^0.41.0", "@google/genai": "^1.0.1", "@kangfenmao/keyv-storage": "^0.1.0", "@mistralai/mistralai": "^1.6.0", "@mozilla/readability": "^0.6.0", "@notionhq/client": "^2.2.15", "@opentelemetry/api": "^1.9.0", "@opentelemetry/core": "^2.0.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-web": "^2.0.0", "@reduxjs/toolkit": "^2.2.5", "@tanstack/react-query": "^5.27.0", "@tanstack/react-virtual": "^3.13.12", "@tryfabric/martian": "^1.2.4", "@viz-js/lang-dot": "^1.0.5", "@viz-js/viz": "^3.14.0", "@xyflow/react": "^12.4.4", "antd": "^5.24.7", "axios": "^1.7.3", "browser-image-compression": "^2.0.2", "color": "^5.0.0", "country-flag-emoji-polyfill": "^0.1.8", "dayjs": "^1.11.11", "dexie": "^4.0.8", "dexie-react-hooks": "^1.1.7", "emittery": "^1.0.3", "emoji-picker-element": "^1.22.1", "fast-diff": "^1.3.0", "fast-xml-parser": "^5.2.0", "franc-min": "^6.2.0", "html-to-image": "^1.11.13", "jaison": "^2.0.2", "js-base64": "^3.7.7", "katex": "^0.16.11", "ky": "^1.7.2", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "markdown-it": "^14.1.0", "mermaid": "^11.7.0", "mime": "^4.0.4", "motion": "^12.10.5", "notion-helper": "^1.3.22", "npx-scope-finder": "^1.2.0", "openai": "^5.1.0", "p-queue": "^8.1.0", "pako": "^2.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hotkeys-hook": "^4.6.1", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "react-redux": "^9.1.2", "react-router": "6", "react-router-dom": "6", "react-spinners": "^0.14.1", "react-window": "^1.8.11", "redux": "^5.0.1", "redux-persist": "^6.0.0", "reflect-metadata": "^0.2.2", "rehype-katex": "^7.0.1", "rehype-mathjax": "^7.1.0", "rehype-raw": "^7.0.0", "remark-cjk-friendly": "^1.2.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-stringify": "^11.0.0", "remove-markdown": "^0.6.2", "styled-components": "^6.1.11", "tiny-pinyin": "^1.3.2", "tokenx": "^1.1.0", "turndown": "^7.2.0", "uuid": "^10.0.0", "zod": "^3.25.74"}, "devDependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@emotion/is-prop-valid": "^1.3.1", "@eslint-react/eslint-plugin": "^1.36.1", "@eslint/js": "^9.22.0", "@hello-pangea/dnd": "^16.6.0", "@shikijs/markdown-it": "^3.7.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/lodash": "^4.17.5", "@types/markdown-it": "^14", "@types/node": "^18.19.9", "@types/pako": "^1.0.2", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/react-infinite-scroll-component": "^5.0.0", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@uiw/codemirror-extensions-langs": "^4.23.14", "@uiw/codemirror-themes-all": "^4.23.14", "@uiw/react-codemirror": "^4.23.14", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.88.0", "shiki": "^3.7.0", "typescript": "^5.6.2", "vite": "^6.2.6", "vitest": "^3.1.4"}, "packageManager": "npm@10.0.0"}