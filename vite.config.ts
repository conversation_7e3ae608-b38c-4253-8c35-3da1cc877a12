import react from '@vitejs/plugin-react-swc'
import { defineConfig } from 'vite'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'

const visualizerPlugin = () => {
  return process.env.VISUALIZER ? [visualizer({ open: true })] : []
}

const isDev = process.env.NODE_ENV === 'development'
const isProd = process.env.NODE_ENV === 'production'

export default defineConfig({
  plugins: [
    react({
      // 移除有问题的SWC插件配置
      // tsDecorators: true
    }),
    ...visualizerPlugin()
  ],
  resolve: {
    alias: {
      '@renderer': resolve(__dirname, 'src/renderer/src'),
      '@shared': resolve(__dirname, 'packages/shared'),
      '@logger': resolve(__dirname, 'src/renderer/src/services/LoggerService'),
      '@mcp-trace/trace-core': resolve(__dirname, 'packages/mcp-trace/trace-core'),
      '@mcp-trace/trace-web': resolve(__dirname, 'packages/mcp-trace/trace-web'),
      '@types': resolve(__dirname, 'src/renderer/src/types')
    }
  },
  root: 'src/renderer',
  publicDir: resolve(__dirname, 'src/renderer/public'),
  build: {
    outDir: resolve(__dirname, 'dist'),
    emptyOutDir: true,
    target: 'esnext',
    rollupOptions: {
      input: {
        index: resolve(__dirname, 'src/renderer/index.html')
      }
    }
  },
  optimizeDeps: {
    exclude: ['pyodide'],
    esbuildOptions: {
      target: 'esnext'
    }
  },
  worker: {
    format: 'es'
  },
  esbuild: isProd ? { legalComments: 'none' } : {},
  server: {
    port: 9018,
    host: true,
    open: true
  },
  define: {
    // 为Web版本定义一些全局变量
    __WEB_VERSION__: true,
    __ELECTRON_VERSION__: false
  }
})
