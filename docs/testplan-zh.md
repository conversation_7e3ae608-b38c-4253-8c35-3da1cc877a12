# 测试计划

为了给用户提供更稳定的应用体验，并提供更快的迭代速度，ZChat推出“测试计划”。

## 用户指南

测试计划分为RC版通道和Beta版通道吗，区别在于：

- **RC版（预览版）**：RC即Release Candidate，功能已经稳定，BUG较少，接近正式版
- **Beta版（测试版）**：功能可能随时变化，BUG较多，可以较早体验未来功能

用户可以在软件的`设置`-`关于`中，开启“测试计划”并选择版本通道。请注意“测试计划”的版本无法保证数据的一致性，请使用前一定要备份数据。

用户在测试过程中发现的BUG，欢迎提交issue或通过其他渠道反馈。用户的反馈对我们非常重要。

## 开发者指南

### 参与测试计划

开发者按照[贡献者指南](CONTRIBUTING.zh.md)要求正常提交`PR`（并注意提交target为`main`）。仓库维护者会综合考虑（例如该功能对应用的影响程度，功能的重要性，是否需要更广泛的测试等），决定该`PR`是否应加入测试计划。

若该`PR`加入测试计划，仓库维护者会做如下操作：

- 通知`PR`提交人
- 设置PR为`draft`状态（避免在测试完成前意外并入`main`）
- `milestone`设置为具体测试计划版本
- 修改`PR`标题

`PR`提交人在参与测试计划过程中，应做到：

- 保持`PR`分支与最新`main`同步（即`PR`分支总是应基于最新`main`代码）
- 保持`PR`分支为无冲突状态
- 积极响应 comments & reviews，修复bug
- 开启维护者可以修改`PR`分支的权限，以便维护者能随时修改BUG

加入测试计划并不保证`PR`的最终合并，也有可能由于功能不成熟或测试反馈不佳而搁置

### 测试计划负责人

某个维护者会被指定为某个版本期间（例如`1.5.0-rc`）的测试计划负责人。测试计划负责人的工作为：

- 判断某个`PR`是否符合测试计划要求，并决定是否应合入当期测试计划
- 修改加入测试计划的`PR`状态，并与`PR`提交人沟通相关事宜
- 在测试计划发版前，将加入测试计划的`PR`分支逐一合并（采用squash merge）至`testplan`对应版本分支，并解决冲突
- 保证`testplan`分支与最新`main`同步
- 负责测试计划发版

## 深入理解

### 关于`PR`

`PR`是特定分支（及commits）、comments、reviews等各种信息的集合，也是测试计划的**最小管理单元**。

相比将所有功能都提交到某个分支，测试计划通过`PR`来管理功能，这可以带来极大的灵活度和效率：

- 测试计划的各个版本间，可以随意增减功能，而无需繁琐的`revert`操作
- 明确了功能边界和负责人，bug修复在各自`PR`中完成，隔离了交叉影响，也能更好观察进度
- `PR`提交人负责与最新`main`之间的冲突；测试计划负责人负责各`PR`分支之间的冲突，但因加入测试计划的各功能相对比较独立（话句话说，如果功能牵涉较广，则应独立上测试计划），冲突一般比较少或简单。

### `testplan`分支

`testplan`分支是用于测试计划发版所用的**临时**分支。

注意：

- **请勿基于该分支开发**。该分支随时会变化甚至删除，且并不保证commit的完整和顺序。
- **请勿向该分支提交`commit`及`PR`**，将不会得到保留
- `testplan`分支总是基于最新`main`分支（而不是基于已发布版本），在其之上添加功能

#### RC版分支

分支名称：`testplan/rc/x.y.z`

用于RC版的发版，x.y.z为目标版本号，注意无论是rc.1还是rc.5，只要主版本号为x.y.z，都在该分支完成。

一般而言，该分支发版的版本号命名为`x.y.z-rc.n`

#### Beta版分支

分支名称：`testplan/beta/x.y.z`

用于Beta版的发版，x.y.z为目标版本号，注意无论是beta.1还是beta.5，只要主版本号为x.y.z，都在该分支完成。

一般而言，该分支发版的版本号命名为`x.y.z-beta.n`

### 版本规则

测试计划的应用版本号为：`x.y.z-CHA.n`，其中：

- `x.y.z`为一般意义上的版本号，在这里称为**目标版本号**
- `CHA`为通道号（Channel），现在分为`rc`和`beta`
- `n`为发版编号，从`1`计数

完整的版本号举例：`1.5.0-rc.3`、`1.5.1-beta.1`、`1.6.0-beta.6`

测试计划的**目标版本号**指向希望添加这些功能的正式版版本号。例如：

- `1.5.0-rc.3`是指，这是`1.5.0`正式版的预览版（当前最新正式版是`1.4.9`，而`1.5.0`正式版还未发布）
- `1.5.1-beta.1`是指，这是`1.5.1`正式版的测试版（当前最新正式版是`1.5.0`，而`1.5.1`正式版还未发布）
