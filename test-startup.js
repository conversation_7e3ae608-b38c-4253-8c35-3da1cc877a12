#!/usr/bin/env node

/**
 * ZChat 启动性能测试脚本
 * 用于测试和监控应用启动性能
 */

const puppeteer = require('puppeteer')
const fs = require('fs')
const path = require('path')

async function testStartupPerformance() {
  console.log('🚀 开始测试 ZChat 启动性能...')
  
  const browser = await puppeteer.launch({
    headless: false, // 设置为 false 可以看到浏览器
    devtools: true
  })
  
  const page = await browser.newPage()
  
  // 监听控制台输出
  page.on('console', msg => {
    if (msg.text().includes('🚀') || msg.text().includes('启动性能报告')) {
      console.log('📊', msg.text())
    }
  })
  
  // 开始性能监控
  await page.tracing.start({
    path: 'startup-trace.json',
    screenshots: true
  })
  
  const startTime = Date.now()
  
  try {
    // 访问应用
    await page.goto('http://localhost:9019', {
      waitUntil: 'networkidle0',
      timeout: 30000
    })
    
    // 等待应用完全加载
    await page.waitForSelector('#root', { timeout: 10000 })
    
    // 等待加载动画消失
    await page.waitForFunction(() => {
      const spinner = document.getElementById('spinner')
      return !spinner || spinner.style.display === 'none'
    }, { timeout: 15000 })
    
    // 等待主要内容加载
    await page.waitForSelector('#home-page', { timeout: 10000 })
    
    const endTime = Date.now()
    const totalTime = endTime - startTime
    
    console.log(`✅ 应用启动完成！总时间: ${totalTime}ms`)
    
    // 获取性能指标
    const performanceMetrics = await page.evaluate(() => {
      return {
        // 获取 Navigation Timing API 数据
        navigation: performance.getEntriesByType('navigation')[0],
        // 获取 Paint Timing API 数据
        paint: performance.getEntriesByType('paint'),
        // 获取自定义性能标记
        marks: performance.getEntriesByType('mark'),
        // 获取内存使用情况（如果支持）
        memory: performance.memory ? {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
        } : null
      }
    })
    
    // 生成性能报告
    const report = {
      timestamp: new Date().toISOString(),
      totalStartupTime: totalTime,
      metrics: performanceMetrics,
      breakdown: {
        domContentLoaded: performanceMetrics.navigation.domContentLoadedEventEnd - performanceMetrics.navigation.navigationStart,
        loadComplete: performanceMetrics.navigation.loadEventEnd - performanceMetrics.navigation.navigationStart,
        firstPaint: performanceMetrics.paint.find(p => p.name === 'first-paint')?.startTime || 0,
        firstContentfulPaint: performanceMetrics.paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0
      }
    }
    
    // 保存报告
    fs.writeFileSync('startup-performance-report.json', JSON.stringify(report, null, 2))
    
    console.log('\n📊 性能报告:')
    console.log(`   总启动时间: ${totalTime}ms`)
    console.log(`   DOM加载完成: ${report.breakdown.domContentLoaded.toFixed(2)}ms`)
    console.log(`   页面加载完成: ${report.breakdown.loadComplete.toFixed(2)}ms`)
    console.log(`   首次绘制: ${report.breakdown.firstPaint.toFixed(2)}ms`)
    console.log(`   首次内容绘制: ${report.breakdown.firstContentfulPaint.toFixed(2)}ms`)
    
    if (report.metrics.memory) {
      console.log(`   内存使用: ${(report.metrics.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`)
    }
    
    // 截图
    await page.screenshot({ 
      path: 'startup-screenshot.png',
      fullPage: true
    })
    
    console.log('\n📁 生成的文件:')
    console.log('   - startup-performance-report.json (性能报告)')
    console.log('   - startup-trace.json (性能追踪)')
    console.log('   - startup-screenshot.png (截图)')
    
  } catch (error) {
    console.error('❌ 启动测试失败:', error.message)
  } finally {
    // 停止性能追踪
    await page.tracing.stop()
    
    // 等待一段时间让用户查看结果
    console.log('\n⏳ 5秒后自动关闭浏览器...')
    setTimeout(async () => {
      await browser.close()
      console.log('✅ 测试完成！')
    }, 5000)
  }
}

// 检查是否安装了 puppeteer
try {
  require.resolve('puppeteer')
} catch (e) {
  console.log('❌ 请先安装 puppeteer:')
  console.log('   npm install puppeteer')
  process.exit(1)
}

// 运行测试
testStartupPerformance().catch(console.error)
