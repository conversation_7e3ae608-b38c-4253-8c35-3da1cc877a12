#!/usr/bin/env node

/**
 * 最终语法修复脚本 - 彻底解决所有语法错误
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

function repairFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  const originalContent = content
  
  // 超级全面的语法修复
  content = content
    // 1. 修复对象定义中的语法错误
    .replace(/{\s*([^}]*),\s*}\s*([^,}])/g, '{ $1 },\n  $2')
    .replace(/{\s*([^}]*)\s*}\s*([^,}])/g, '{ $1 },\n  $2')
    
    // 2. 修复import语句
    .replace(/}\s*([A-Z][a-zA-Z]*)\s*}\s*from/g, ', $1 } from')
    .replace(/,\s*}\s*([A-Z][a-zA-Z]*)\s*}\s*from/g, ', $1 } from')
    
    // 3. 修复数组和对象的语法
    .replace(/,\s*}\s*([^,}])/g, ' },\n  $1')
    .replace(/enabled:\s*true,\s*}\s*([^,}])/g, 'enabled: true },\n  $1')
    
    // 4. 修复函数调用和对象字面量
    .replace(/(\w+):\s*([^,}]+),\s*}\s*([^,}])/g, '$1: $2 },\n  $3')
    
    // 5. 修复createSlice和reducer定义
    .replace(/\}\s*\n\s*export\s+const\s+\{/g, '})\n\nexport const {')
    
    // 6. 修复interface定义
    .replace(/export interface [^{]+{\s*[^}]*\}\)/g, (match) => {
      return match.replace(/\}\)/, '}')
    })
    
    // 7. 修复多余的闭合括号
    .replace(/\}\)\s*\n\s*export/g, '}\n\nexport')
    .replace(/\}\)\s*\n\s*\}/g, '}\n}')
    
    // 8. 修复Promise和setTimeout
    .replace(/(new\s+Promise|setTimeout)\([^)]*\n\s*([^)])/g, (match, p1, p2) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    })
    
    // 9. 修复console和logger调用
    .replace(/(console\.\w+|logger\.\w+)\([^)]*\n\s*([^)])/g, (match, p1, p2) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, ')\n    $1')
      }
      return match
    })
    
    // 10. 修复window.api调用
    .replace(/window\.api\.[^(]+\([^)]*\n\s*([^)])/g, (match, p1) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n  $1')
      }
      return match
    })
    
    // 11. 修复JSON.parse调用
    .replace(/JSON\.parse\(await [^)]+\n\s*([^)])/g, (match, p1) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    })
    
    // 12. 修复import语句
    .replace(/import\([^)]*\n\s*([^)])/g, (match, p1) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n    $1')
      }
      return match
    })
    
    // 13. 修复特殊的语法错误模式
    .replace(/\}\s*\)\s*\n\s*export/g, '}\n\nexport')
    .replace(/\}\s*\)\s*\n\s*\}/g, '}\n}')
    .replace(/\}\s*\)\s*\n\s*\)/g, '}\n)')
    
    // 14. 清理多余的空行
    .replace(/\n\n\n+/g, '\n\n')
    
    // 15. 修复缺少分号的语句
    .replace(/([^;])\s*\n\s*export/g, '$1\n\nexport')
    
    // 16. 修复特定的错误模式
    .replace(/\}\s*\n\s*\}\s*\n\s*export/g, '}\n}\n\nexport')
    .replace(/\}\s*\n\s*\}\s*\n\s*\}/g, '}\n}\n}')
    
    // 17. 修复对象属性定义
    .replace(/(\w+):\s*([^,}]+),\s*}\s*(\w+):/g, '$1: $2,\n  $3:')
    
    // 18. 修复数组元素
    .replace(/\[\s*([^]]*),\s*\]/g, '[ $1 ]')
    
    // 19. 修复函数参数
    .replace(/\(\s*([^)]*),\s*\)/g, '( $1 )')
    
    // 20. 修复条件语句
    .replace(/if\s*\(\s*([^)]*),\s*\)/g, 'if ( $1 )')
  
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    modified = true
  }
  
  return modified
}

function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      callback(filePath)
    }
  })
}

function main() {
  console.log('🔧 开始最终语法修复...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (repairFile(filePath)) {
      modifiedFiles++
      const relativePath = path.relative(path.join(__dirname, '..'), filePath)
      console.log(`✅ 最终修复: ${relativePath}`)
    }
  })
  
  console.log(`\n📊 最终修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 所有语法错误已彻底修复！')
}

main()
