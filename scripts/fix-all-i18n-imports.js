#!/usr/bin/env node

/**
 * 修复所有剩余的i18n导入和使用
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 移除i18n导入
  const i18nImportPatterns = [
    /import\s+i18n\s+from\s+['"]@renderer\/i18n['"]\s*\n?/g,
    /import\s*{\s*[^}]*i18n[^}]*\s*}\s*from\s*['"]@renderer\/i18n['"]\s*\n?/g,
    /import\s*{\s*[^}]*getLanguageCode[^}]*\s*}\s*from\s*['"]@renderer\/i18n['"]\s*\n?/g
  ]
  
  i18nImportPatterns.forEach(pattern => {
    if (pattern.test(content)) {
      content = content.replace(pattern, '')
      modified = true
    }
  })
  
  // 替换i18n.t()调用为直接的中文文本
  const i18nCallPatterns = [
    { pattern: /i18n\.t\(['"`]([^'"`]+)['"`]\)/g, replacement: (match, key) => `'${generateChineseFromKey(key)}'` },
    { pattern: /i18next\.['"`]([^'"`]+)['"`]/g, replacement: (match, text) => `'${text}'` }
  ]
  
  i18nCallPatterns.forEach(({ pattern, replacement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement)
      modified = true
    }
  })
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
    console.log(`✅ 修复i18n: ${filePath}`)
  }
  
  return modified
}

// 从key生成中文文本
function generateChineseFromKey(key) {
  const keyMappings = {
    'success': '成功',
    'failed': '失败',
    'error': '错误',
    'warning': '警告',
    'info': '信息',
    'confirm': '确认',
    'cancel': '取消',
    'save': '保存',
    'delete': '删除',
    'edit': '编辑',
    'add': '添加',
    'search': '搜索',
    'loading': '加载中',
    'copy': '复制',
    'paste': '粘贴',
    'cut': '剪切',
    'undo': '撤销',
    'redo': '重做',
    'close': '关闭',
    'open': '打开',
    'new': '新建',
    'file': '文件',
    'folder': '文件夹',
    'settings': '设置',
    'help': '帮助',
    'about': '关于'
  }
  
  // 尝试从key的最后一部分生成中文
  const parts = key.split('.')
  const lastPart = parts[parts.length - 1]
  
  if (keyMappings[lastPart]) {
    return keyMappings[lastPart]
  }
  
  // 如果无法映射，返回最后一部分
  return lastPart
}

// 主函数
function main() {
  console.log('🔧 开始修复所有i18n导入和使用...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 所有i18n导入和使用已修复！')
}

// 运行脚本
main()
