#!/usr/bin/env node

/**
 * 修复语法错误
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 修复常见的语法错误
  const fixes = [
    // 修复多余的括号和花括号
    { pattern: /\}\s*\)\s*\}/g, replacement: '}' },
    { pattern: /\)\s*\}\s*\)/g, replacement: ')' },
    { pattern: /\{\s*\)\s*\}/g, replacement: '{}' },
    { pattern: /\(\s*\{\s*\)/g, replacement: '()' },
    
    // 修复label()调用
    { pattern: /['"`]([^'"`]+)['"`]\.label\(\s*\)\s*\}\s*\)/g, replacement: "'$1'" },
    { pattern: /['"`]([^'"`]+)['"`]\.label\(\s*\)/g, replacement: "'$1'" },
    
    // 修复多余的占位符
    { pattern: /'占位符'\s*\}\s*\)/g, replacement: "'请输入...'" },
    { pattern: /'占位符'\s*\)/g, replacement: "'请输入...'" },
    
    // 修复其他常见错误
    { pattern: /\s*\}\s*\)\s*$/gm, replacement: '' },
    { pattern: /\s*\)\s*\}\s*$/gm, replacement: '' }
  ]
  
  fixes.forEach(({ pattern, replacement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement)
      modified = true
    }
  })
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
    console.log(`✅ 修复语法错误: ${filePath}`)
  }
  
  return modified
}

// 主函数
function main() {
  console.log('🔧 开始修复语法错误...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 语法错误修复完成！')
}

// 运行脚本
main()
