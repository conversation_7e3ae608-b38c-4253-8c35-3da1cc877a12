#!/usr/bin/env node

/**
 * 修复所有数字导入错误
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 移除所有数字导入
  const numericImportPattern = /import\s*{\s*\d+\s*}\s*from\s*['"]@renderer\/hooks\/useTexts['"]\s*\n?/g
  if (numericImportPattern.test(content)) {
    content = content.replace(numericImportPattern, '')
    modified = true
    console.log(`✅ 移除数字导入: ${filePath}`)
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
  }
  
  return modified
}

// 主函数
function main() {
  console.log('🔧 开始修复数字导入错误...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 数字导入错误修复完成！')
}

// 运行脚本
main()
