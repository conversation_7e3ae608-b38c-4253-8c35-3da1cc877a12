#!/bin/bash

# 最终全面修复脚本

echo "🔧 开始最终全面修复..."

# 修复所有TypeScript/TSX文件的语法错误
find src/renderer/src -name "*.ts" -o -name "*.tsx" | while read file; do
  echo "修复文件: $file"
  
  # 使用sed进行批量修复
  sed -i '' \
    -e 's/}\s*}\s*export/}\nexport/g' \
    -e 's/}\s*}\s*}/}\n}/g' \
    -e 's/}\s*}\s*catch/} catch/g' \
    -e 's/}\s*}\s*finally/} finally/g' \
    -e 's/}\s*}\s*else/} else/g' \
    -e 's/}\s*}\s*if/}\nif/g' \
    -e 's/}\s*}\s*for/}\nfor/g' \
    -e 's/}\s*}\s*while/}\nwhile/g' \
    -e 's/}\s*}\s*return/}\nreturn/g' \
    -e 's/}\s*}\s*const/}\nconst/g' \
    -e 's/}\s*}\s*let/}\nlet/g' \
    -e 's/}\s*}\s*var/}\nvar/g' \
    -e 's/}\s*}\s*function/}\nfunction/g' \
    -e 's/}\s*}\s*class/}\nclass/g' \
    -e 's/}\s*}\s*interface/}\ninterface/g' \
    -e 's/}\s*}\s*type/}\ntype/g' \
    -e 's/}\s*}\s*enum/}\nenum/g' \
    -e 's/}\s*}\s*import/}\nimport/g' \
    -e 's/}\s*}\s*async/}\nasync/g' \
    -e 's/}\s*}\s*await/}\nawait/g' \
    -e 's/}\s*}\s*try/}\ntry/g' \
    -e 's/}\s*}\s*switch/}\nswitch/g' \
    -e 's/}\s*}\s*case/}\ncase/g' \
    -e 's/}\s*}\s*default/}\ndefault/g' \
    -e 's/}\s*}\s*break/}\nbreak/g' \
    -e 's/}\s*}\s*continue/}\ncontinue/g' \
    -e 's/}\s*}\s*throw/}\nthrow/g' \
    -e 's/}\s*}\s*delete/}\ndelete/g' \
    -e 's/}\s*}\s*new/}\nnew/g' \
    -e 's/}\s*}\s*this/}\nthis/g' \
    -e 's/}\s*}\s*super/}\nsuper/g' \
    -e 's/}\s*}\s*window/}\nwindow/g' \
    -e 's/}\s*}\s*document/}\ndocument/g' \
    -e 's/}\s*}\s*console/}\nconsole/g' \
    -e 's/}\s*}\s*logger/}\nlogger/g' \
    -e 's/}\s*}\s*JSON/}\nJSON/g' \
    -e 's/}\s*}\s*Math/}\nMath/g' \
    -e 's/}\s*}\s*Date/}\nDate/g' \
    -e 's/}\s*}\s*Array/}\nArray/g' \
    -e 's/}\s*}\s*Object/}\nObject/g' \
    -e 's/}\s*}\s*String/}\nString/g' \
    -e 's/}\s*}\s*Number/}\nNumber/g' \
    -e 's/}\s*}\s*Boolean/}\nBoolean/g' \
    -e 's/}\s*}\s*Promise/}\nPromise/g' \
    -e 's/}\s*}\s*Error/}\nError/g' \
    -e 's/}\s*}\s*RegExp/}\nRegExp/g' \
    -e 's/}\s*}\s*Symbol/}\nSymbol/g' \
    -e 's/}\s*}\s*Map/}\nMap/g' \
    -e 's/}\s*}\s*Set/}\nSet/g' \
    -e 's/}\s*}\s*WeakMap/}\nWeakMap/g' \
    -e 's/}\s*}\s*WeakSet/}\nWeakSet/g' \
    -e 's/}\s*}\s*Proxy/}\nProxy/g' \
    -e 's/}\s*}\s*Reflect/}\nReflect/g' \
    "$file"
done

echo "✅ 最终全面修复完成！"
