#!/usr/bin/env node

/**
 * 检查剩余的t()调用脚本
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      // 跳过node_modules和.git目录
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 检查单个文件
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  const lines = content.split('\n')
  
  // 检查是否有useTranslation或useTexts导入
  const hasTranslationImport = content.includes('useTranslation') || content.includes('useTexts')
  
  // 查找可疑的t()调用
  const suspiciousCalls = []
  
  lines.forEach((line, index) => {
    // 匹配t('...')或t("...")模式，但排除一些常见的非i18n调用
    const tCallRegex = /\bt\(['"`][^'"`]*['"`]\)/g
    const matches = line.match(tCallRegex)
    
    if (matches) {
      // 排除一些明显不是i18n的调用
      const filteredMatches = matches.filter(match => {
        // 排除setTimeout, setInterval等
        if (line.includes('setTimeout') || line.includes('setInterval')) return false
        // 排除logger调用
        if (line.includes('logger.')) return false
        // 排除数据库调用
        if (line.includes('.table(') || line.includes('.get(') || line.includes('.put(')) return false
        // 排除函数定义
        if (line.includes('function t(') || line.includes('const t =') || line.includes('= t =>')) return false
        
        return true
      })
      
      if (filteredMatches.length > 0) {
        suspiciousCalls.push({
          line: index + 1,
          content: line.trim(),
          matches: filteredMatches
        })
      }
    }
  })
  
  if (suspiciousCalls.length > 0) {
    console.log(`\n🚨 发现可疑的t()调用: ${filePath}`)
    console.log(`   有翻译导入: ${hasTranslationImport ? '是' : '否'}`)
    suspiciousCalls.forEach(call => {
      console.log(`   第${call.line}行: ${call.content}`)
      call.matches.forEach(match => {
        console.log(`     -> ${match}`)
      })
    })
    return true
  }
  
  return false
}

// 主函数
function main() {
  console.log('🔍 检查剩余的t()调用...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let problematicFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (checkFile(filePath)) {
      problematicFiles++
    }
  })
  
  console.log(`\n📊 检查完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   问题文件: ${problematicFiles}`)
  
  if (problematicFiles === 0) {
    console.log('\n✅ 没有发现问题！所有t()调用都已正确处理。')
  } else {
    console.log('\n⚠️  发现问题文件，请手动检查和修复。')
  }
}

// 运行脚本
main()
