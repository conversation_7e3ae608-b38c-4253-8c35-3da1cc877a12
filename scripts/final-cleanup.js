#!/usr/bin/env node

/**
 * 最终清理脚本 - 修复所有剩余的语法错误
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  const originalContent = content
  
  // 修复所有常见的语法错误
  content = content
    // 1. 修复缺少闭合括号的函数调用
    .replace(/(\w+\([^)]*)\n\s*([^)])/g, (match, p1, p2) => {
      if (!p1.includes('))')) {
        return `${p1})\n  ${p2}`
      }
      return match
    })
    
    // 2. 修复缺少闭合括号的对象
    .replace(/{\s*([^}]*content:[^}]*)\n\s*([^}])/g, (match, p1, p2) => {
      if (!p1.includes('})')) {
        return `{ ${p1} })\n  ${p2}`
      }
      return match
    })
    
    // 3. 修复多余的闭合括号
    .replace(/\}\)\s*\n\s*export/g, '}\n\nexport')
    .replace(/\}\)\s*\n\s*\}/g, '}\n}')
    
    // 4. 修复export语句前的语法错误
    .replace(/([^}])\s*\n\s*export/g, '$1\n\nexport')
    
    // 5. 修复createSlice缺少闭合
    .replace(/\}\s*\n\s*export\s+const\s+\{/g, '})\n\nexport const {')
    
    // 6. 修复interface定义
    .replace(/export interface [^{]+{\s*[^}]*\}\)/g, (match) => {
      return match.replace(/\}\)/, '}')
    })
    
    // 7. 修复Promise和setTimeout
    .replace(/(new\s+Promise|setTimeout)\([^)]*\n\s*([^)])/g, (match, p1, p2) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    })
    
    // 8. 修复console和logger调用
    .replace(/(console\.\w+|logger\.\w+)\([^)]*\n\s*([^)])/g, (match, p1, p2) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, ')\n    $1')
      }
      return match
    })
    
    // 9. 修复JSON.parse调用
    .replace(/JSON\.parse\(await [^)]+\n\s*([^)])/g, (match, p1) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    })
    
    // 10. 修复window.api调用
    .replace(/window\.api\.[^(]+\([^)]*\n\s*([^)])/g, (match, p1) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n  $1')
      }
      return match
    })
    
    // 11. 修复if语句和try-catch缺少闭合
    .replace(/(if\s*\([^)]+\)\s*{[^}]*)\n\s*([^}])/g, (match, p1, p2) => {
      if (!p1.includes('}\n')) {
        return `${p1}\n  }\n  ${p2}`
      }
      return match
    })
    
    // 12. 修复缺少分号的语句
    .replace(/([^;])\s*\n\s*export/g, '$1\n\nexport')
    
    // 13. 清理多余的空行
    .replace(/\n\n\n+/g, '\n\n')
    
    // 14. 修复特殊的语法错误模式
    .replace(/\}\s*\)\s*\n\s*export/g, '}\n\nexport')
    .replace(/\}\s*\)\s*\n\s*\}/g, '}\n}')
  
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    modified = true
  }
  
  return modified
}

function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      callback(filePath)
    }
  })
}

function main() {
  console.log('🔧 开始最终清理...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (fixFile(filePath)) {
      modifiedFiles++
      const relativePath = path.relative(path.join(__dirname, '..'), filePath)
      console.log(`✅ 清理: ${relativePath}`)
    }
  })
  
  console.log(`\n📊 清理完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 最终清理完成！')
}

main()
