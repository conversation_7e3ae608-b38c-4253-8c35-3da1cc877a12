#!/usr/bin/env node

/**
 * 修复所有剩余的语法错误
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 修复常见的语法错误
  const fixes = [
    // 修复缺少闭合括号的情况
    { pattern: /\}\s*\n\s*export\s+const\s+\{/g, replacement: '})\n\nexport const {' },
    { pattern: /\}\s*\n\s*export\s+/g, replacement: '})\n\nexport ' },
    
    // 修复函数调用缺少闭合括号
    { pattern: /window\.api\.[^(]+\([^)]*\n\s*if\s*\(/g, replacement: (match) => {
      const lines = match.split('\n')
      if (lines[0] && !lines[0].endsWith(')')) {
        lines[0] += ')'
      }
      return lines.join('\n')
    }},
    
    // 修复对象字面量缺少闭合括号
    { pattern: /{\s*content:\s*[^}]+\n\s*[^}]/g, replacement: (match) => {
      if (!match.includes('})')) {
        return match.replace(/\n\s*([^}])/, ' })\n  $1')
      }
      return match
    }},
    
    // 修复数组和对象缺少闭合
    { pattern: /\[\s*{\s*name:\s*[^}]+\n\s*if\s*\(/g, replacement: (match) => {
      return match.replace(/\n\s*if\s*\(/, ' }] })\n  if (')
    }},
    
    // 修复Promise缺少闭合括号
    { pattern: /new\s+Promise\([^)]*\n\s*[^)]/g, replacement: (match) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    }},
    
    // 修复setTimeout缺少闭合括号
    { pattern: /setTimeout\([^)]*\n\s*[^)]/g, replacement: (match) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    }},
    
    // 修复window.message调用缺少闭合括号
    { pattern: /window\.message\.[^(]+\(\s*{\s*[^}]+\n\s*[^}]/g, replacement: (match) => {
      if (!match.includes('})')) {
        return match.replace(/\n\s*([^}])/, ' })\n    $1')
      }
      return match
    }},
    
    // 修复createSlice缺少闭合
    { pattern: /createSlice\(\s*{\s*[^}]*reducers:\s*{[^}]*\}\s*\n\s*export/g, replacement: (match) => {
      return match.replace(/\n\s*export/, '})\n\nexport')
    }}
  ]
  
  fixes.forEach(({ pattern, replacement }) => {
    const originalContent = content
    if (typeof replacement === 'function') {
      content = content.replace(pattern, replacement)
    } else {
      content = content.replace(pattern, replacement)
    }
    if (content !== originalContent) {
      modified = true
    }
  })
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
    console.log(`✅ 修复语法错误: ${filePath}`)
  }
  
  return modified
}

// 主函数
function main() {
  console.log('🔧 开始修复所有语法错误...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 所有语法错误修复完成！')
}

// 运行脚本
main()
