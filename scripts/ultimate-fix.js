#!/usr/bin/env node

/**
 * 终极修复脚本 - 一次性解决所有语法错误
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  const originalContent = content
  
  // 超级全面的语法修复
  content = content
    // 1. 修复所有缺少闭合括号的情况
    .replace(/(\w+\([^)]*)\n\s*([^)])/g, (match, p1, p2) => {
      if (!p1.includes('))') && !p1.includes('})')) {
        return `${p1})\n  ${p2}`
      }
      return match
    })
    
    // 2. 修复对象和数组缺少闭合
    .replace(/{\s*([^}]*)\n\s*([^}])/g, (match, p1, p2) => {
      if (p1.includes(':') && !p1.includes('}')) {
        return `{ ${p1} }\n  ${p2}`
      }
      return match
    })
    
    // 3. 修复useEffect和其他hooks
    .replace(/(useEffect\s*\(\s*\(\s*\)\s*=>\s*{\s*[^}]*)\n\s*([^}])/g, (match, p1, p2) => {
      if (!p1.includes('}, []')) {
        return `${p1}\n    })\n  }, [])\n\n  ${p2}`
      }
      return match
    })
    
    // 4. 修复try-catch语句
    .replace(/(} catch \([^)]+\) {\s*[^}]*)\n\s*([^}])/g, (match, p1, p2) => {
      if (!p1.includes('}\n')) {
        return `${p1}\n  }\n  ${p2}`
      }
      return match
    })
    
    // 5. 修复if语句
    .replace(/(if \([^)]+\) {\s*[^}]*)\n\s*([^}])/g, (match, p1, p2) => {
      if (!p1.includes('}\n')) {
        return `${p1}\n  }\n  ${p2}`
      }
      return match
    })
    
    // 6. 修复函数定义
    .replace(/(function\s+\w+\s*\([^)]*\)\s*{\s*[^}]*)\n\s*([^}])/g, (match, p1, p2) => {
      if (!p1.includes('}\n')) {
        return `${p1}\n}\n\n${p2}`
      }
      return match
    })
    
    // 7. 修复箭头函数
    .replace(/(\(\s*\)\s*=>\s*{\s*[^}]*)\n\s*([^}])/g, (match, p1, p2) => {
      if (!p1.includes('}\n')) {
        return `${p1}\n  }\n  ${p2}`
      }
      return match
    })
    
    // 8. 修复export语句前的语法错误
    .replace(/([^}])\s*\n\s*export/g, '$1\n\nexport')
    
    // 9. 修复多余的闭合括号
    .replace(/\}\)\s*\n\s*export/g, '}\n\nexport')
    .replace(/\}\)\s*\n\s*\}/g, '}\n}')
    
    // 10. 修复createSlice
    .replace(/\}\s*\n\s*export\s+const\s+\{/g, '})\n\nexport const {')
    
    // 11. 修复interface定义
    .replace(/export interface [^{]+{\s*[^}]*\}\)/g, (match) => {
      return match.replace(/\}\)/, '}')
    })
    
    // 12. 修复Promise和setTimeout
    .replace(/(new\s+Promise|setTimeout)\([^)]*\n\s*([^)])/g, (match, p1, p2) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    })
    
    // 13. 修复console和logger调用
    .replace(/(console\.\w+|logger\.\w+)\([^)]*\n\s*([^)])/g, (match, p1, p2) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, ')\n    $1')
      }
      return match
    })
    
    // 14. 修复window.api调用
    .replace(/window\.api\.[^(]+\([^)]*\n\s*([^)])/g, (match, p1) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n  $1')
      }
      return match
    })
    
    // 15. 修复JSON.parse调用
    .replace(/JSON\.parse\(await [^)]+\n\s*([^)])/g, (match, p1) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    })
    
    // 16. 修复import语句
    .replace(/import\([^)]*\n\s*([^)])/g, (match, p1) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n    $1')
      }
      return match
    })
    
    // 17. 修复特殊的语法错误模式
    .replace(/\}\s*\)\s*\n\s*export/g, '}\n\nexport')
    .replace(/\}\s*\)\s*\n\s*\}/g, '}\n}')
    .replace(/\}\s*\)\s*\n\s*\)/g, '}\n)')
    
    // 18. 清理多余的空行
    .replace(/\n\n\n+/g, '\n\n')
    
    // 19. 修复缺少分号的语句
    .replace(/([^;])\s*\n\s*export/g, '$1\n\nexport')
    
    // 20. 修复特定的错误模式
    .replace(/\}\s*\n\s*\}\s*\n\s*export/g, '}\n}\n\nexport')
    .replace(/\}\s*\n\s*\}\s*\n\s*\}/g, '}\n}\n}')
  
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    modified = true
  }
  
  return modified
}

function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      callback(filePath)
    }
  })
}

function main() {
  console.log('🔧 开始终极修复...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (fixFile(filePath)) {
      modifiedFiles++
      const relativePath = path.relative(path.join(__dirname, '..'), filePath)
      console.log(`✅ 终极修复: ${relativePath}`)
    }
  })
  
  console.log(`\n📊 终极修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 所有语法错误已修复！')
}

main()
