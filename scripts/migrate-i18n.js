#!/usr/bin/env node

/**
 * i18n迁移脚本
 * 将useTranslation替换为useTexts
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 需要替换的导入语句
const importReplacements = [
  {
    from: "import { useTranslation } from 'react-i18next'",
    to: "import { useTranslation } from '@renderer/hooks/useTexts'"
  },
  {
    from: 'import { useTranslation } from "react-i18next"',
    to: 'import { useTranslation } from "@renderer/hooks/useTexts"'
  }
]

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      // 跳过node_modules和.git目录
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 替换导入语句
  importReplacements.forEach(replacement => {
    if (content.includes(replacement.from)) {
      content = content.replace(replacement.from, replacement.to)
      modified = true
      console.log(`✅ 替换导入语句: ${filePath}`)
    }
  })
  
  // 如果文件被修改，写回文件
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
  }
  
  return modified
}

// 主函数
function main() {
  console.log('🚀 开始i18n迁移...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 迁移完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  
  if (modifiedFiles > 0) {
    console.log('\n⚠️  注意事项:')
    console.log('   1. 请检查修改的文件，确保导入路径正确')
    console.log('   2. 某些复杂的i18n用法可能需要手动调整')
    console.log('   3. 建议运行测试确保功能正常')
  }
}

// 运行脚本
main()
