#!/usr/bin/env node

/**
 * 手动修复特殊语法错误
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 特定文件的修复
const specificFixes = {
  'src/renderer/src/App.tsx': (content) => {
    return content
      .replace(/useEffect\(\(\) => {\s*}\s*\/\/ 异步初始化数据库\s*}\s*}, \[\]\)/g, 
        `useEffect(() => {
    // 异步初始化数据库
    import('@renderer/databases').then(({ initDatabase }) => {
      initDatabase().then(() => {
        setDbReady(true)
      })
    })
  }, [])`)
  },
  
  'src/renderer/src/init.ts': (content) => {
    return content
      .replace(/window\.keyv\.init\(\)\s*}\s*} catch \(error\) {\s*}\s*}\s*}/g,
        `window.keyv.init()
  } catch (error) {
    console.warn('KeyvStorage initialization failed in web environment:', error)
    // 为Web环境提供一个简单的存储实现
    window.keyv = {
      get: async () => null,
      set: async () => {},
      delete: async () => {},
      clear: async () => {},
      init: () => {}
    }
  }`)
  }
}

// 通用修复规则
const generalFixes = [
  // 修复多余的闭合括号
  { pattern: /\}\s*\}\s*catch/g, replacement: '} catch' },
  { pattern: /\}\s*\}\s*export/g, replacement: '}\n\nexport' },
  { pattern: /\}\s*\}\s*\}/g, replacement: '}\n}' },
  
  // 修复useEffect
  { pattern: /useEffect\(\(\) => {\s*}\s*\/\/ ([^}]+)\s*}\s*}, \[\]\)/g, 
    replacement: (match, comment) => {
      return `useEffect(() => {
    // ${comment}
  }, [])`
    }
  },
  
  // 修复try-catch
  { pattern: /try {\s*([^}]+)\s*}\s*} catch/g, 
    replacement: (match, content) => {
      return `try {
    ${content.trim()}
  } catch`
    }
  },
  
  // 修复if语句
  { pattern: /if \([^)]+\) {\s*}\s*([^}])/g,
    replacement: (match, p1, p2) => {
      return `if (${p1}) {
    // TODO: implement
  }
  ${p2}`
    }
  }
]

function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  const originalContent = content
  
  // 应用特定文件修复
  const relativePath = path.relative(path.join(__dirname, '..'), filePath)
  if (specificFixes[relativePath]) {
    content = specificFixes[relativePath](content)
  }
  
  // 应用通用修复
  generalFixes.forEach(({ pattern, replacement }) => {
    if (typeof replacement === 'function') {
      content = content.replace(pattern, replacement)
    } else {
      content = content.replace(pattern, replacement)
    }
  })
  
  // 额外的清理
  content = content
    // 清理多余的空行
    .replace(/\n\n\n+/g, '\n\n')
    // 修复缺少的闭合括号
    .replace(/(\w+\([^)]*)\n\s*([^)])/g, (match, p1, p2) => {
      if (!p1.includes('))')) {
        return `${p1})\n  ${p2}`
      }
      return match
    })
    // 修复对象语法
    .replace(/{\s*([^}]*)\n\s*([^}])/g, (match, p1, p2) => {
      if (p1.includes(':') && !p1.includes('}')) {
        return `{ ${p1} }\n  ${p2}`
      }
      return match
    })
  
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    modified = true
  }
  
  return modified
}

function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      callback(filePath)
    }
  })
}

function main() {
  console.log('🔧 开始手动修复特殊语法错误...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (fixFile(filePath)) {
      modifiedFiles++
      const relativePath = path.relative(path.join(__dirname, '..'), filePath)
      console.log(`✅ 手动修复: ${relativePath}`)
    }
  })
  
  console.log(`\n📊 手动修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 特殊语法错误已修复！')
}

main()
