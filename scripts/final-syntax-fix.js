#!/usr/bin/env node

/**
 * 最终语法修复脚本
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要特殊处理的文件
const specialFixes = {
  'src/renderer/src/services/StoreSyncService.ts': (content) => {
    // 确保类定义正确闭合
    if (!content.includes('export const storeSyncService')) {
      return content.replace(
        /export const storeSyncService = StoreSyncService\.getInstance\(\)/,
        'export const storeSyncService = StoreSyncService.getInstance()'
      )
    }
    return content
  },
  
  'src/renderer/src/services/NutstoreService.ts': (content) => {
    // 修复函数定义问题
    return content
      .replace(/\}\s*\n\s*export async function/g, '}\n\nexport async function')
      .replace(/\}\s*\)\s*\n\s*export/g, '})\n\nexport')
  },
  
  'src/renderer/src/services/BackupService.ts': (content) => {
    // 修复多余的括号
    return content
      .replace(/\}\)\s*\n\s*export/g, '}\n\nexport')
      .replace(/return false\s*\}\)/g, 'return false\n}')
  },
  
  'src/renderer/src/store/websearch.ts': (content) => {
    // 修复interface定义
    return content
      .replace(/\}\)\s*\n\s*export interface/g, '}\n\nexport interface')
      .replace(/\}\)\s*\n\s*export const/g, '}\n\nexport const')
  },
  
  'src/renderer/src/store/settings.ts': (content) => {
    // 修复interface定义
    return content
      .replace(/extends RemoteSyncState \{\}\)/g, 'extends RemoteSyncState {}')
  }
}

// 通用修复规则
const generalFixes = [
  // 修复缺少闭合括号的函数调用
  { pattern: /window\.api\.[^(]+\([^)]*\n\s*[^)]/g, replacement: (match) => {
    if (!match.includes('))')) {
      return match.replace(/\n\s*([^)])/, '))\n  $1')
    }
    return match
  }},
  
  // 修复缺少闭合括号的对象
  { pattern: /{\s*[^}]*\n\s*[^}]/g, replacement: (match) => {
    if (match.includes('content:') && !match.includes('})')) {
      return match.replace(/\n\s*([^}])/, ' })\n  $1')
    }
    return match
  }},
  
  // 修复createSlice缺少闭合
  { pattern: /\}\s*\n\s*export const \{/g, replacement: '})\n\nexport const {' },
  
  // 修复interface定义
  { pattern: /\}\)\s*\n\s*export/g, replacement: '}\n\nexport' },
  
  // 修复多余的闭合括号
  { pattern: /\}\)\s*\n\s*export/g, replacement: '}\n\nexport' }
]

function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 应用特殊修复
  const relativePath = path.relative(path.join(__dirname, '..'), filePath)
  if (specialFixes[relativePath]) {
    const newContent = specialFixes[relativePath](content)
    if (newContent !== content) {
      content = newContent
      modified = true
    }
  }
  
  // 应用通用修复
  generalFixes.forEach(({ pattern, replacement }) => {
    const originalContent = content
    if (typeof replacement === 'function') {
      content = content.replace(pattern, replacement)
    } else {
      content = content.replace(pattern, replacement)
    }
    if (content !== originalContent) {
      modified = true
    }
  })
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
    console.log(`✅ 修复: ${relativePath}`)
  }
  
  return modified
}

function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      callback(filePath)
    }
  })
}

function main() {
  console.log('🔧 开始最终语法修复...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (fixFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 最终语法修复完成！')
}

main()
