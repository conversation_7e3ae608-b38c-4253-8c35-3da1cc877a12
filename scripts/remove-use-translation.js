#!/usr/bin/env node

/**
 * 移除所有useTranslation导入和相关代码
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 移除useTranslation导入
  const importPatterns = [
    /import\s*{\s*useTranslation\s*}\s*from\s*['"]@renderer\/hooks\/useTexts['"]\s*\n?/g,
    /import\s*{\s*useTranslation\s*}\s*from\s*['"]react-i18next['"]\s*\n?/g,
    /import\s*{\s*useTranslation\s*,\s*([^}]+)\s*}\s*from\s*['"]@renderer\/hooks\/useTexts['"]/g,
    /import\s*{\s*([^}]+),\s*useTranslation\s*}\s*from\s*['"]@renderer\/hooks\/useTexts['"]/g
  ]
  
  importPatterns.forEach(pattern => {
    if (pattern.test(content)) {
      content = content.replace(pattern, (match, otherImports) => {
        if (otherImports) {
          // 如果有其他导入，保留它们
          return `import { ${otherImports} } from '@renderer/hooks/useTexts'\n`
        }
        return '' // 完全移除导入
      })
      modified = true
    }
  })
  
  // 移除useTranslation Hook调用
  const hookPatterns = [
    /const\s*{\s*t\s*}\s*=\s*useTranslation\(\)\s*\n?/g,
    /const\s*{\s*t\s*,\s*[^}]+\s*}\s*=\s*useTranslation\(\)\s*\n?/g,
    /const\s*{\s*[^}]+,\s*t\s*}\s*=\s*useTranslation\(\)\s*\n?/g
  ]
  
  hookPatterns.forEach(pattern => {
    if (pattern.test(content)) {
      content = content.replace(pattern, '')
      modified = true
    }
  })
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
    console.log(`✅ 移除useTranslation: ${filePath}`)
  }
  
  return modified
}

// 主函数
function main() {
  console.log('🗑️ 开始移除useTranslation依赖...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 移除完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ useTranslation依赖已完全移除！')
}

// 运行脚本
main()
