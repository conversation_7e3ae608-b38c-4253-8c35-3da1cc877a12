#!/usr/bin/env node

/**
 * 修复剩余的i18n.t()调用脚本
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// i18n.t()调用的替换映射
const i18nReplacements = {
  // 备份相关
  "i18n.t('message.backup.success')": "'备份成功'",
  "i18n.t('message.backup.failed')": "'备份失败'",
  "i18n.t('message.restore.success')": "'恢复成功'",
  "i18n.t('message.restore.failed')": "'恢复失败'",
  "i18n.t('error.backup.file_format')": "'备份文件格式错误'",
  "i18n.t('common.success')": "'成功'",
  "i18n.t('common.warning')": "'警告'",
  "i18n.t('message.reset.confirm.content')": "'确定要重置所有数据吗？此操作不可撤销。'",
  "i18n.t('message.reset.double.confirm.title')": "'二次确认'",
  "i18n.t('message.reset.double.confirm.content')": "'请再次确认是否要重置所有数据？'",
  "i18n.t('message.error.invalid.nutstore_token')": "'坚果云Token无效'",

  // 翻译相关
  "i18n.t('translate.error.not_configured')": "'翻译模型未配置'",
  "i18n.t('translate.error.failed')": "'翻译失败'",

  // 助手相关
  "i18n.t('assistant.default.name')": "'默认助手'",
  "i18n.t(`assistant.${assistant.id}.name`)": "assistant.name || '助手'",
  "i18n.t(`assistant.${assistant.id}.topic.name`)": "topic.name || '新话题'",
  "i18n.t(`assistant.default.name`)": "'默认助手'",

  // 导出相关
  "i18n.t('export.markdown.title')": "'导出为Markdown'",
  "i18n.t('export.pdf.title')": "'导出为PDF'",
  "i18n.t('export.word.title')": "'导出为Word'",
  "i18n.t('export.html.title')": "'导出为HTML'",
  "i18n.t('export.success')": "'导出成功'",
  "i18n.t('export.failed')": "'导出失败'",

  // API相关
  "i18n.t('api.error.network')": "'网络错误'",
  "i18n.t('api.error.timeout')": "'请求超时'",
  "i18n.t('api.error.unauthorized')": "'未授权'",
  "i18n.t('api.error.forbidden')": "'访问被禁止'",
  "i18n.t('api.error.not_found')": "'资源未找到'",
  "i18n.t('api.error.server')": "'服务器错误'",

  // 文件相关
  "i18n.t('file.upload.success')": "'文件上传成功'",
  "i18n.t('file.upload.failed')": "'文件上传失败'",
  "i18n.t('file.download.success')": "'文件下载成功'",
  "i18n.t('file.download.failed')": "'文件下载失败'",
  "i18n.t('file.delete.success')": "'文件删除成功'",
  "i18n.t('file.delete.failed')": "'文件删除失败'",

  // 设置相关
  "i18n.t('settings.save.success')": "'设置保存成功'",
  "i18n.t('settings.save.failed')": "'设置保存失败'",
  "i18n.t('settings.reset.success')": "'设置重置成功'",
  "i18n.t('settings.reset.failed')": "'设置重置失败'",

  // 通用消息
  "i18n.t('common.error')": "'错误'",
  "i18n.t('common.info')": "'信息'",
  "i18n.t('common.confirm')": "'确认'",
  "i18n.t('common.cancel')": "'取消'",
  "i18n.t('common.save')": "'保存'",
  "i18n.t('common.delete')": "'删除'",
  "i18n.t('common.edit')": "'编辑'",
  "i18n.t('common.add')": "'添加'",
  "i18n.t('common.loading')": "'加载中...'",
  "i18n.t('common.copied')": "'已复制'",
  "i18n.t('common.failed')": "'失败'"
}

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      // 跳过node_modules和.git目录
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false

  // 替换已知的i18n.t()调用
  Object.entries(i18nReplacements).forEach(([from, to]) => {
    if (content.includes(from)) {
      content = content.replaceAll(from, to)
      modified = true
      console.log(`✅ 替换 ${from} -> ${to} in ${filePath}`)
    }
  })

  // 使用正则表达式匹配所有剩余的i18n.t()调用
  const i18nRegex = /i18n\.t\(['"`]([^'"`]+)['"`]\)/g
  const matches = content.match(i18nRegex)

  if (matches) {
    matches.forEach(match => {
      // 提取key
      const keyMatch = match.match(/i18n\.t\(['"`]([^'"`]+)['"`]\)/)
      if (keyMatch) {
        const key = keyMatch[1]
        // 简单的key到中文的映射
        const chineseText = keyToChinese(key)
        content = content.replaceAll(match, `'${chineseText}'`)
        modified = true
        console.log(`🔄 自动替换 ${match} -> '${chineseText}' in ${filePath}`)
      }
    })
  }

  // 如果文件被修改，写回文件
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
  }

  return modified
}

// 简单的key到中文的映射函数
function keyToChinese(key) {
  // 基于key的结构推断中文含义
  const keyMap = {
    // 通用
    'save': '保存',
    'cancel': '取消',
    'confirm': '确认',
    'delete': '删除',
    'edit': '编辑',
    'add': '添加',
    'loading': '加载中...',
    'success': '成功',
    'failed': '失败',
    'error': '错误',
    'warning': '警告',
    'info': '信息',

    // 文件操作
    'upload': '上传',
    'download': '下载',
    'export': '导出',
    'import': '导入',

    // 消息
    'copied': '已复制',
    'saved': '已保存',
    'deleted': '已删除'
  }

  // 尝试从key中提取最后一个单词
  const parts = key.split('.')
  const lastPart = parts[parts.length - 1]

  if (keyMap[lastPart]) {
    return keyMap[lastPart]
  }

  // 如果没有找到映射，返回key本身
  return key
}

// 主函数
function main() {
  console.log('🚀 开始修复i18n.t()调用...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
}

// 运行脚本
main()
