#!/usr/bin/env node

/**
 * 更新文本fallback映射脚本
 * 基于控制台输出的缺失文本路径，自动生成合理的中文fallback
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 常见的缺失文本路径和对应的中文翻译
const missingTexts = {
  'sidebar.collapse': '收起侧边栏',
  'minapp.title': '小应用',
  'assistants.abbr': '助手',
  'common.topics': '话题',
  'assistants.tags.untagged': '未分类',
  'chat.add.assistant.title': '添加助手',
  'assistants.edit.title': '编辑助手',
  'assistants.copy.title': '复制助手',
  'assistants.icon.type': '图标类型',
  'settings.assistant.icon.type.model': '模型图标',
  'settings.assistant.icon.type.emoji': '表情图标',
  'settings.assistant.icon.type.none': '无图标',
  'assistants.tags.manage': '管理标签',
  'assistants.tags.add': '添加标签',
  'assistants.list.showByTags': '按标签显示',
  'assistants.list.showByList': '列表显示',
  'assistants.search': '搜索助手',
  'assistants.settings.title': '助手设置',
  'assistants.settings.more': '更多设置',
  'assistants.settings.model': '模型设置',
  'assistants.settings.prompt': '提示词设置',
  'assistants.settings.knowledge_base': '知识库设置',
  'assistants.settings.mcp': 'MCP设置',
  'assistants.settings.regular_phrases.title': '常用短语',
  'assistants.settings.tool_use_mode': '工具调用方式',
  'assistants.settings.reasoning_effort': '思维链长度',
  'assistants.settings.reasoning_effort.default': '默认',
  'assistants.settings.reasoning_effort.low': '浮想',
  'assistants.settings.reasoning_effort.medium': '斟酌',
  'assistants.settings.reasoning_effort.high': '沉思',
  'assistants.settings.reasoning_effort.off': '关闭',
  'assistants.save.title': '保存到智能体',
  'assistants.save.success': '保存成功',
  'assistants.manage.title': '管理智能体',
  'assistants.my_agents': '我的智能体',
  'assistants.search.no_results': '没有找到相关智能体',
  'assistants.sorting.title': '排序',
  'assistants.tag.agent': '智能体',
  'assistants.tag.default': '默认',
  'assistants.tag.new': '新建',
  'assistants.tag.system': '系统',
  'assistants.export.agent': '导出智能体',
  'assistants.import.title': '从外部导入',
  'assistants.import.button': '导入',
  'assistants.import.select_file': '选择文件',
  'assistants.import.file_filter': 'JSON 文件',
  'assistants.import.type.file': '文件',
  'assistants.import.type.url': 'URL',
  'assistants.import.url_placeholder': '输入 JSON URL',
  'assistants.import.error.url_required': '请输入 URL',
  'assistants.import.error.fetch_failed': '从 URL 获取数据失败',
  'assistants.import.error.invalid_format': '无效的代理格式：缺少必填字段',
  'assistants.add.button': '添加到助手',
  'assistants.add.name': '名称',
  'assistants.add.name.placeholder': '输入名称',
  'assistants.add.prompt': '提示词',
  'assistants.add.prompt.placeholder': '输入提示词',
  'assistants.add.knowledge_base': '知识库',
  'assistants.add.knowledge_base.placeholder': '选择知识库',
  'assistants.add.title': '创建智能体',
  'assistants.add.unsaved_changes_warning': '你有未保存的内容，确定要关闭吗？',
  'assistants.delete.popup.content': '确定要删除此智能体吗？',
  'assistants.edit.model.select.title': '选择模型'
}

function updateUseTextsFile() {
  const useTextsPath = path.join(__dirname, '../src/renderer/src/hooks/useTexts.ts')
  let content = fs.readFileSync(useTextsPath, 'utf8')
  
  // 查找fallbackMap的位置
  const fallbackMapStart = content.indexOf('const fallbackMap: Record<string, string> = {')
  const fallbackMapEnd = content.indexOf('}', fallbackMapStart) + 1
  
  if (fallbackMapStart === -1) {
    console.error('Could not find fallbackMap in useTexts.ts')
    return false
  }
  
  // 生成新的fallbackMap内容
  const fallbackEntries = Object.entries(missingTexts)
    .map(([key, value]) => `    '${key}': '${value}'`)
    .join(',\n')
  
  const newFallbackMap = `const fallbackMap: Record<string, string> = {
${fallbackEntries}
  }`
  
  // 替换内容
  const newContent = content.substring(0, fallbackMapStart) + 
                    newFallbackMap + 
                    content.substring(fallbackMapEnd)
  
  fs.writeFileSync(useTextsPath, newContent, 'utf8')
  console.log('✅ Updated useTexts.ts with new fallback mappings')
  return true
}

function main() {
  console.log('🔄 Updating text fallback mappings...')
  
  if (updateUseTextsFile()) {
    console.log(`📊 Added ${Object.keys(missingTexts).length} fallback mappings`)
    console.log('✅ Text fallback update complete!')
  } else {
    console.log('❌ Failed to update text fallbacks')
  }
}

// 运行脚本
main()
