#!/usr/bin/env node

/**
 * 完全移除多语言功能，将所有t()调用替换为直接的中文文本
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 完整的文本映射表
const textMappings = {
  // 设置相关
  'settings.provider.title': '模型提供商',
  'settings.provider.search': '搜索提供商',
  'provider.${provider.id}': '提供商',
  'settings.provider.api_key': 'API密钥',
  'settings.provider.api_host': 'API地址',
  'settings.provider.check': '检查连接',
  'settings.provider.get_api_key': '获取API密钥',
  'common.models': '模型',
  'button.add': '添加',
  'settings.display.title': '显示设置',
  'settings.mcp.title': 'MCP设置',
  'settings.tool.title': '工具设置',
  'memory.title': '记忆设置',
  'settings.shortcuts.title': '快捷键设置',
  'settings.quickAssistant.title': '快速助手',
  'selection.name': '选择助手',
  'settings.quickPhrase.title': '快速短语',
  'settings.data.title': '数据设置',
  'settings.about': '关于',
  
  // 助手相关
  'assistants.title': '助手',
  'assistants.abbr': '助手',
  'assistants.tags.untagged': '未分类',
  'assistants.edit.title': '编辑助手',
  'assistants.copy.title': '复制助手',
  'assistants.icon.type': '图标类型',
  'settings.assistant.icon.type.model': '模型图标',
  'settings.assistant.icon.type.emoji': '表情图标',
  'settings.assistant.icon.type.none': '无图标',
  'assistants.tags.manage': '管理标签',
  'assistants.tags.add': '添加标签',
  'assistants.list.showByTags': '按标签显示',
  
  // 聊天相关
  'chat.add.assistant.title': '添加助手',
  'chat.default.description': '默认聊天助手',
  'chat.assistant.search.placeholder': '搜索助手...',
  
  // 通用
  'common.sort.pinyin.asc': '拼音升序',
  'common.sort.pinyin.desc': '拼音降序',
  'common.topics': '话题',
  'button.includes_user_questions': '包含用户问题',
  'button.case_sensitive': '区分大小写',
  'button.whole_word': '全词匹配',
  'settings.quickPanel.close': '关闭',
  'settings.quickPanel.select': '选择',
  
  // 侧边栏
  'sidebar.collapse': '收起侧边栏',
  'minapp.title': '小应用'
}

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 替换所有t('key')调用为直接的中文文本
  Object.entries(textMappings).forEach(([key, chinese]) => {
    const patterns = [
      `t('${key}')`,
      `t("${key}")`,
      `{t('${key}')}`,
      `{t("${key}")}`,
      `\${t('${key}')}`,
      `\${t("${key}")}`
    ]
    
    patterns.forEach(pattern => {
      if (content.includes(pattern)) {
        content = content.replaceAll(pattern, `'${chinese}'`)
        modified = true
        console.log(`✅ 替换 ${pattern} -> '${chinese}' in ${filePath}`)
      }
    })
  })
  
  // 使用正则表达式匹配其他t()调用并替换为key本身
  const tCallRegex = /\bt\(['"`]([^'"`]+)['"`]\)/g
  let match
  while ((match = tCallRegex.exec(content)) !== null) {
    const fullMatch = match[0]
    const key = match[1]
    
    // 跳过已经处理过的
    if (textMappings[key]) continue
    
    // 生成合理的中文文本
    const chineseText = generateChineseFromKey(key)
    content = content.replace(fullMatch, `'${chineseText}'`)
    modified = true
    console.log(`🔄 自动替换 ${fullMatch} -> '${chineseText}' in ${filePath}`)
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
  }
  
  return modified
}

// 从key生成中文文本
function generateChineseFromKey(key) {
  const keyMappings = {
    'title': '标题',
    'name': '名称',
    'description': '描述',
    'settings': '设置',
    'button': '按钮',
    'add': '添加',
    'edit': '编辑',
    'delete': '删除',
    'save': '保存',
    'cancel': '取消',
    'confirm': '确认',
    'search': '搜索',
    'placeholder': '占位符',
    'provider': '提供商',
    'model': '模型',
    'api': 'API',
    'key': '密钥',
    'host': '地址',
    'check': '检查',
    'display': '显示',
    'tool': '工具',
    'shortcuts': '快捷键',
    'data': '数据',
    'about': '关于'
  }
  
  // 尝试从key的最后一部分生成中文
  const parts = key.split('.')
  const lastPart = parts[parts.length - 1]
  
  if (keyMappings[lastPart]) {
    return keyMappings[lastPart]
  }
  
  // 如果无法映射，返回最后一部分
  return lastPart
}

// 主函数
function main() {
  console.log('🚀 开始完全移除多语言功能...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 处理完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 多语言功能已完全移除，所有文本已替换为中文！')
}

// 运行脚本
main()
