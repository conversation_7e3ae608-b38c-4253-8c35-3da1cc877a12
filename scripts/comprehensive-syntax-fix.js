#!/usr/bin/env node

/**
 * 全面语法修复脚本 - 一次性修复所有语法错误
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 全面的修复规则
const comprehensiveFixes = [
  // 1. 修复缺少闭合括号的函数调用
  {
    pattern: /window\.api\.[^(]+\([^)]*\n\s*[^)]/g,
    replacement: (match) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    }
  },
  
  // 2. 修复window.message调用缺少闭合括号
  {
    pattern: /window\.message\.[^(]+\(\s*{\s*[^}]+\n\s*[^}]/g,
    replacement: (match) => {
      if (!match.includes('})')) {
        return match.replace(/\n\s*([^}])/, ' })\n    $1')
      }
      return match
    }
  },
  
  // 3. 修复Promise和setTimeout缺少闭合括号
  {
    pattern: /(new\s+Promise|setTimeout)\([^)]*\n\s*[^)]/g,
    replacement: (match) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    }
  },
  
  // 4. 修复对象字面量缺少闭合括号
  {
    pattern: /{\s*[^}]*content:\s*[^}]+\n\s*[^}]/g,
    replacement: (match) => {
      if (!match.includes('})')) {
        return match.replace(/\n\s*([^}])/, ' })\n  $1')
      }
      return match
    }
  },
  
  // 5. 修复数组缺少闭合括号
  {
    pattern: /\[\s*{\s*name:\s*[^}]+\n\s*[^}]/g,
    replacement: (match) => {
      if (!match.includes('}]')) {
        return match.replace(/\n\s*([^}])/, ' }]\n  $1')
      }
      return match
    }
  },
  
  // 6. 修复createSlice缺少闭合
  {
    pattern: /createSlice\(\s*{[^}]*reducers:\s*{[^}]*\}\s*\n\s*export/g,
    replacement: (match) => {
      return match.replace(/\n\s*export/, '})\n\nexport')
    }
  },
  
  // 7. 修复interface定义多余的括号
  {
    pattern: /export interface [^{]+{\s*[^}]*\}\)/g,
    replacement: (match) => {
      return match.replace(/\}\)/, '}')
    }
  },
  
  // 8. 修复函数定义缺少闭合括号
  {
    pattern: /\}\s*\n\s*export\s+(const|function|async\s+function)/g,
    replacement: '})\n\nexport $1'
  },
  
  // 9. 修复try-catch缺少闭合
  {
    pattern: /} catch \([^)]+\) {\s*[^}]+\n\s*[^}]/g,
    replacement: (match) => {
      if (!match.includes('}\n')) {
        return match.replace(/\n\s*([^}])/, '\n  }\n  $1')
      }
      return match
    }
  },
  
  // 10. 修复if语句缺少闭合
  {
    pattern: /if \([^)]+\) {\s*[^}]+\n\s*[^}]/g,
    replacement: (match) => {
      if (!match.includes('}\n')) {
        return match.replace(/\n\s*([^}])/, '\n  }\n  $1')
      }
      return match
    }
  },
  
  // 11. 修复console.warn缺少闭合括号
  {
    pattern: /console\.warn\([^)]*\n\s*[^)]/g,
    replacement: (match) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, ')\n    $1')
      }
      return match
    }
  },
  
  // 12. 修复logger.error缺少闭合括号
  {
    pattern: /logger\.error\([^)]*\n\s*[^)]/g,
    replacement: (match) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, ')\n    $1')
      }
      return match
    }
  },
  
  // 13. 修复JSON.parse缺少闭合括号
  {
    pattern: /JSON\.parse\(await [^)]+\n\s*[^)]/g,
    replacement: (match) => {
      if (!match.includes('))')) {
        return match.replace(/\n\s*([^)])/, '))\n      $1')
      }
      return match
    }
  },
  
  // 14. 修复多余的闭合括号
  {
    pattern: /\}\)\s*\n\s*export/g,
    replacement: '}\n\nexport'
  },
  
  // 15. 修复缺少分号
  {
    pattern: /\}\s*\n\s*export\s+const\s+\{/g,
    replacement: '})\n\nexport const {'
  }
]

function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  const originalContent = content
  
  // 应用所有修复规则
  comprehensiveFixes.forEach(({ pattern, replacement }, index) => {
    try {
      if (typeof replacement === 'function') {
        content = content.replace(pattern, replacement)
      } else {
        content = content.replace(pattern, replacement)
      }
    } catch (error) {
      console.warn(`修复规则 ${index + 1} 在文件 ${filePath} 中出错:`, error.message)
    }
  })
  
  // 特殊处理一些常见的语法错误模式
  content = content
    // 修复缺少闭合的函数调用
    .replace(/(\w+\([^)]*)\n\s*([^)])/g, '$1)\n  $2')
    // 修复缺少闭合的对象
    .replace(/{\s*([^}]*)\n\s*([^}])/g, '{\n  $1\n}\n$2')
    // 修复多余的闭合括号
    .replace(/\}\)\s*\n\s*\}/g, '}\n}')
    // 修复export语句前的语法错误
    .replace(/([^}])\s*\n\s*export/g, '$1\n\nexport')
  
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    modified = true
  }
  
  return modified
}

function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      callback(filePath)
    }
  })
}

function main() {
  console.log('🔧 开始全面语法修复...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (fixFile(filePath)) {
      modifiedFiles++
      const relativePath = path.relative(path.join(__dirname, '..'), filePath)
      console.log(`✅ 修复: ${relativePath}`)
    }
  })
  
  console.log(`\n📊 修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 全面语法修复完成！')
}

main()
