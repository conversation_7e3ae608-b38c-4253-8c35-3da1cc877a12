#!/usr/bin/env node

/**
 * 修复import语句的脚本
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

function fixImports(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  const originalContent = content
  
  // 修复import语句的语法错误
  content = content
    // 修复 import { something }, from 'module' 的错误
    .replace(/import\s*{\s*([^}]+)\s*},\s*\n\s*from\s+(['"][^'"]+['"])/g, "import { $1 } from $2")
    
    // 修复 import something, from 'module' 的错误
    .replace(/import\s+([^,\n]+),\s*\n\s*from\s+(['"][^'"]+['"])/g, "import $1 from $2")
    
    // 修复多行import语句
    .replace(/import\s*{\s*([^}]*)\s*},?\s*\n\s*from\s+(['"][^'"]+['"])/g, "import { $1 } from $2")
    
    // 修复export语句
    .replace(/export\s*{\s*([^}]+)\s*},\s*\n\s*from\s+(['"][^'"]+['"])/g, "export { $1 } from $2")
    
    // 修复type import
    .replace(/import\s+type\s*{\s*([^}]+)\s*},\s*\n\s*from\s+(['"][^'"]+['"])/g, "import type { $1 } from $2")
    
    // 清理多余的逗号和空格
    .replace(/import\s*{\s*([^}]+)\s*,\s*}\s*from/g, "import { $1 } from")
    .replace(/import\s*{\s*,\s*([^}]+)\s*}\s*from/g, "import { $1 } from")
    
    // 修复对象和数组定义中的语法错误
    .replace(/{\s*([^}]*),\s*}\s*([^,}])/g, '{ $1 },\n  $2')
    .replace(/{\s*([^}]*)\s*}\s*([^,}])/g, '{ $1 },\n  $2')
    
    // 修复函数参数
    .replace(/\(\s*([^)]*),\s*\)/g, '($1)')
    
    // 修复数组元素
    .replace(/\[\s*([^]]*),\s*\]/g, '[$1]')
  
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    modified = true
  }
  
  return modified
}

function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      callback(filePath)
    }
  })
}

function main() {
  console.log('🔧 开始修复import语句...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (fixImports(filePath)) {
      modifiedFiles++
      const relativePath = path.relative(path.join(__dirname, '..'), filePath)
      console.log(`✅ 修复import: ${relativePath}`)
    }
  })
  
  console.log(`\n📊 import修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 所有import语句已修复！')
}

main()
