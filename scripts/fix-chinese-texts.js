#!/usr/bin/env node

/**
 * 修复中文文本，将自动生成的不准确文本替换为正确的中文
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 精确的文本替换映射
const textReplacements = {
  // 从截图中看到的需要修复的文本
  "'settings.provider.title'": "'模型提供商'",
  "'settings.provider.search'": "'搜索提供商'",
  "'settings.provider.api_key'": "'API密钥'",
  "'settings.provider.api_host'": "'API地址'",
  "'settings.provider.check'": "'检查连接'",
  "'settings.provider.get_api_key'": "'获取API密钥'",
  "'common.models'": "'模型'",
  "'button.add'": "'添加'",
  "'settings.display.title'": "'显示设置'",
  "'settings.mcp.title'": "'MCP设置'",
  "'settings.tool.title'": "'工具设置'",
  "'memory.title'": "'记忆设置'",
  "'settings.shortcuts.title'": "'快捷键设置'",
  "'settings.quickAssistant.title'": "'快速助手'",
  "'selection.name'": "'选择助手'",
  "'settings.quickPhrase.title'": "'快速短语'",
  "'settings.data.title'": "'数据设置'",
  "'settings.about'": "'关于'",

  // 翻译相关
  "'target_language'": "'目标语言'",
  "'alter_language'": "'切换语言'",
  "'smart_translate_tips'": "'智能翻译提示'",
  "'original_hide'": "'隐藏原文'",
  "'original_copy'": "'复制原文'",
  "'esc_stop'": "'ESC停止'",
  "'esc_close'": "'ESC关闭'",
  "'r_regenerate'": "'R重新生成'",
  "'c_copy'": "'C复制'",
  "'success'": "'成功'",
  "'failed'": "'失败'",

  // 其他常见的key
  "'title'": "'标题'",
  "'name'": "'名称'",
  "'description'": "'描述'",
  "'search'": "'搜索'",
  "'placeholder'": "'占位符'",
  "'add'": "'添加'",
  "'edit'": "'编辑'",
  "'delete'": "'删除'",
  "'save'": "'保存'",
  "'cancel'": "'取消'",
  "'confirm'": "'确认'",
  "'check'": "'检查'",
  "'api'": "'API'",
  "'key'": "'密钥'",
  "'host'": "'地址'",
  "'model'": "'模型'",
  "'provider'": "'提供商'",
  "'data'": "'数据'",
  "'about'": "'关于'",
  "'tool'": "'工具'",
  "'shortcuts'": "'快捷键'",
  "'display'": "'显示'"
}

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)

  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)

    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false

  // 替换不准确的中文文本
  Object.entries(textReplacements).forEach(([from, to]) => {
    if (content.includes(from)) {
      content = content.replaceAll(from, to)
      modified = true
      console.log(`✅ 修复 ${from} -> ${to} in ${filePath}`)
    }
  })

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
  }

  return modified
}

// 主函数
function main() {
  console.log('🔧 开始修复中文文本...')

  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0

  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })

  console.log(`\n📊 修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 中文文本修复完成！')
}

// 运行脚本
main()