#!/usr/bin/env node

/**
 * 修复所有剩余的t()函数调用
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 完整的文本映射表
const textMappings = {
  'code_block.expand': '展开',
  'code_block.wrap.off': '关闭换行',
  'common.selectedItems': '已选择 {count} 项',
  'settings.provider.oauth.button': '使用 {provider} 登录',
  'settings.quickPanel.forward': '前进',
  'html_artifacts.empty_preview': '无内容预览',
  'code_block.edit': '编辑',
  'settings.theme.light': '浅色',
  'settings.theme.dark': '深色',
  'settings.theme.system': '跟随系统',
  'provider.openai': 'OpenAI',
  'provider.anthropic': 'Anthropic',
  'provider.google': 'Google',
  'provider.azure': 'Azure',
  'provider.ollama': 'Ollama',
  'provider.groq': 'Groq',
  'provider.deepseek': 'DeepSeek',
  'provider.moonshot': 'Moonshot',
  'provider.zhipu': '智谱AI',
  'provider.baidu': '百度',
  'provider.alibaba': '阿里云',
  'provider.tencent': '腾讯云',
  'provider.minimax': 'MiniMax',
  'provider.doubao': '豆包',
  'provider.stepfun': '阶跃星辰',
  'provider.siliconflow': 'SiliconFlow',
  'provider.deepbricks': 'DeepBricks',
  'provider.novita': 'Novita',
  'provider.together': 'Together',
  'provider.fireworks': 'Fireworks',
  'provider.perplexity': 'Perplexity',
  'provider.mistral': 'Mistral',
  'provider.cohere': 'Cohere',
  'provider.replicate': 'Replicate',
  'provider.huggingface': 'Hugging Face',
  'provider.custom': '自定义',
  'button.update_available': '发现新版本 {version}',
  'collapse': '收起',
  'on': '开启',
  'preview': '预览',
  'back': '返回'
}

// 需要处理的文件扩展名
const extensions = ['.tsx', '.ts']

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      callback(filePath)
    }
  })
}

// 处理单个文件
function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 替换所有t('key')调用为直接的中文文本
  Object.entries(textMappings).forEach(([key, chinese]) => {
    const patterns = [
      `t('${key}')`,
      `t("${key}")`,
      `t(\`${key}\`)`,
      `{t('${key}')}`,
      `{t("${key}")}`,
      `{t(\`${key}\`)}`,
      `\${t('${key}')}`,
      `\${t("${key}")}`
    ]
    
    patterns.forEach(pattern => {
      if (content.includes(pattern)) {
        content = content.replaceAll(pattern, `'${chinese}'`)
        modified = true
        console.log(`✅ 替换 ${pattern} -> '${chinese}' in ${filePath}`)
      }
    })
  })
  
  // 使用正则表达式匹配其他t()调用并替换为key本身
  const tCallRegex = /\bt\(['"`]([^'"`]+)['"`](?:,\s*[^)]+)?\)/g
  let match
  const replacements = []
  
  while ((match = tCallRegex.exec(content)) !== null) {
    const fullMatch = match[0]
    const key = match[1]
    
    // 跳过已经处理过的
    if (textMappings[key]) continue
    
    // 生成合理的中文文本
    const chineseText = generateChineseFromKey(key)
    replacements.push({ from: fullMatch, to: `'${chineseText}'` })
  }
  
  // 执行替换
  replacements.forEach(({ from, to }) => {
    if (content.includes(from)) {
      content = content.replace(from, to)
      modified = true
      console.log(`🔄 自动替换 ${from} -> ${to} in ${filePath}`)
    }
  })
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
  }
  
  return modified
}

// 从key生成中文文本
function generateChineseFromKey(key) {
  const keyMappings = {
    'title': '标题',
    'name': '名称',
    'description': '描述',
    'settings': '设置',
    'button': '按钮',
    'add': '添加',
    'edit': '编辑',
    'delete': '删除',
    'save': '保存',
    'cancel': '取消',
    'confirm': '确认',
    'search': '搜索',
    'placeholder': '占位符',
    'provider': '提供商',
    'model': '模型',
    'api': 'API',
    'key': '密钥',
    'host': '地址',
    'check': '检查',
    'display': '显示',
    'tool': '工具',
    'shortcuts': '快捷键',
    'data': '数据',
    'about': '关于',
    'expand': '展开',
    'collapse': '收起',
    'wrap': '换行',
    'off': '关闭',
    'on': '开启',
    'preview': '预览',
    'edit': '编辑',
    'back': '返回',
    'forward': '前进',
    'light': '浅色',
    'dark': '深色',
    'system': '系统'
  }
  
  // 尝试从key的最后一部分生成中文
  const parts = key.split('.')
  const lastPart = parts[parts.length - 1]
  
  if (keyMappings[lastPart]) {
    return keyMappings[lastPart]
  }
  
  // 如果无法映射，返回最后一部分
  return lastPart
}

// 主函数
function main() {
  console.log('🔧 开始修复所有剩余的t()调用...')
  
  const srcDir = path.join(__dirname, '../src/renderer/src')
  let processedFiles = 0
  let modifiedFiles = 0
  
  walkDir(srcDir, (filePath) => {
    processedFiles++
    if (processFile(filePath)) {
      modifiedFiles++
    }
  })
  
  console.log(`\n📊 修复完成:`)
  console.log(`   处理文件: ${processedFiles}`)
  console.log(`   修改文件: ${modifiedFiles}`)
  console.log('\n✅ 所有t()调用已修复！')
}

// 运行脚本
main()
