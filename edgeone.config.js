// EdgeOne 部署配置文件
module.exports = {
  // 项目基本信息
  name: 'zchat-ai-assistant',
  version: '1.5.1',
  
  // 构建配置
  build: {
    // 构建命令
    command: 'npm run build',
    // 构建输出目录
    output: 'dist',
    // 环境变量
    env: {
      NODE_ENV: 'production',
      VITE_APP_TITLE: 'ZChat - AI智能助手'
    }
  },
  
  // 部署配置
  deploy: {
    // 静态资源配置
    static: {
      // 缓存配置
      cache: {
        // HTML文件缓存时间（秒）
        html: 3600, // 1小时
        // CSS/JS文件缓存时间
        assets: 31536000, // 1年
        // 图片文件缓存时间
        images: 2592000, // 30天
        // 字体文件缓存时间
        fonts: 31536000 // 1年
      },
      
      // 压缩配置
      compression: {
        gzip: true,
        brotli: true
      }
    },
    
    // 路由配置
    routes: [
      {
        // SPA路由重写
        source: '/(.*)',
        destination: '/index.html',
        type: 'rewrite'
      }
    ],
    
    // 安全头配置
    headers: [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ]
      },
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/manifest+json'
          }
        ]
      }
    ]
  },
  
  // 域名配置
  domains: [
    // 主域名
    'zchat.example.com',
    // 备用域名
    'ai.example.com'
  ],
  
  // SSL配置
  ssl: {
    // 自动申请SSL证书
    auto: true,
    // 强制HTTPS
    force: true
  },
  
  // CDN配置
  cdn: {
    // 启用CDN加速
    enabled: true,
    // 缓存策略
    caching: {
      // 静态资源缓存
      static: '1y',
      // HTML缓存
      html: '1h'
    }
  },
  
  // 监控配置
  monitoring: {
    // 性能监控
    performance: true,
    // 错误监控
    errors: true,
    // 访问日志
    access: true
  }
}
